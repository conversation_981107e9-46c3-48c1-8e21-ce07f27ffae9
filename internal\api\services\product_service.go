package services

import (
	"errors"
	"fmt"
	"time"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/errs"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/types"

	"gorm.io/gorm"
)

type ProductService struct{}

var ProductServiceInstance = &ProductService{}

func (s *ProductService) GetAllProducts() ([]types.Product, error) {
	var order []types.Product
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	query := `
	SELECT 
	*
	FROM PRODUCTS
	`
	err = db.Raw(query).Scan(&order).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return order, nil
}

func (s *ProductService) GetProduct(orderCode string) ([]types.Product, error) {
	var order []types.Product
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	queryProduct := `SELECT 
	*
	FROM PRODUCTS
	WHERE product_code = ?`
	err = db.Raw(queryProduct, orderCode).Scan(&order).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	return order, nil
}

func (s *ProductService) AddProduct(product *request.AddProductRequest) error {
	productEntity := &types.Product{
		ProductName:   product.ProductName,
		ProductWeight: product.ProductWeight,
		InjectionTime: product.InjectionTime,
		Color:         product.Color,
		UnitPrice:     product.UnitPrice,
	}

	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	// 檢查是否重複
	var existing types.Product
	queryCheck := `
	SELECT * FROM PRODUCTS
	WHERE product_name = ? AND product_weight = ? AND injection_time = ? AND color = ? AND unit_price = ?
	LIMIT 1`
	err = db.Raw(queryCheck,
		productEntity.ProductName, productEntity.ProductWeight, productEntity.InjectionTime, productEntity.Color, productEntity.UnitPrice).
		Scan(&existing).Error
	if err == nil && existing.ID != 0 {
		return &errs.ErrProductExists{ProductID: existing.ID}
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		fmt.Println("Query error:", err)
		return err
	}

	// 產生 product_code
	var lastCode string
	err = db.Raw(`SELECT product_code FROM products ORDER BY product_code DESC LIMIT 1`).Scan(&lastCode).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		fmt.Println("Failed to get last product_code:", err)
		return err
	}
	newCode := generateNextCode("P", lastCode)
	productEntity.ProductCode = newCode
	productEntity.CreatedAt = time.Now()

	// 交易處理
	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(productEntity).Error; err != nil {
			return err
		}

		inventory := &types.ProductInventory{
			ProductID: productEntity.ID,
			Quantity:  0,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if err := tx.Create(inventory).Error; err != nil {
			return err
		}

		movement := &types.ProductMovement{
			ProductID:    productEntity.ID,
			Quantity:     0,
			MovementType: "INIT",
			Note:         "產品建立時初始化庫存",
			CreatedAt:    time.Now(),
		}
		if err := tx.Create(movement).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		fmt.Println("Insert error:", err)
		return err
	}

	fmt.Println("Product, inventory, and movement inserted:", productEntity.ProductCode)
	return nil
}

func (s *ProductService) AddStock(request *request.StockRequest) error {
	db, err := database.DatabaseConnection()
	if err != nil {
		return err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Exec(`
			UPDATE product_inventories 
			SET quantity = quantity + ?
			WHERE product_id = ?`, request.Quantity, request.ProductID).Error; err != nil {
			return err
		}

		movement := &types.ProductMovement{
			ProductID:    request.ProductID,
			MovementType: "IN",
			Quantity:     request.Quantity,
			Note:         request.Note,
			CreatedAt:    time.Now(),
		}
		if err := tx.Create(movement).Error; err != nil {
			return err
		}

		return nil
	})

	return err
}

func (s *ProductService) ReduceStock(request *request.StockRequest) error {
	db, err := database.DatabaseConnection()
	if err != nil {
		return err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	err = db.Transaction(func(tx *gorm.DB) error {
		var inventory types.ProductInventory
		if err := tx.Raw(`
			SELECT * FROM product_inventories 
			WHERE product_id = ? LIMIT 1`, request.ProductID).Scan(&inventory).Error; err != nil {
			return err
		}

		if inventory.Quantity < request.Quantity {
			return fmt.Errorf("庫存不足，現有 %.2f，欲出庫 %.2f", inventory.Quantity, request.Quantity)
		}

		if err := tx.Exec(`
			UPDATE product_inventories 
			SET quantity = quantity - ?
			WHERE product_id = ?`, request.Quantity, request.ProductID).Error; err != nil {
			return err
		}

		movement := &types.ProductMovement{
			ProductID:    request.ProductID,
			MovementType: "OUT",
			Quantity:     request.Quantity,
			Note:         request.Note,
			CreatedAt:    time.Now(),
		}
		if err := tx.Create(movement).Error; err != nil {
			return err
		}

		return nil
	})

	return err
}
