package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func ProductRouter(router *gin.RouterGroup) {
	router.GET("", controllers.ProductControllerInstance.GetProducts)              // 獲取產品
	router.GET("/:productCode", controllers.ProductControllerInstance.GetProducts) // 獲取特定產品
	router.POST("", controllers.ProductControllerInstance.AddProduct)              // 新增產品
	router.POST("/addstock", controllers.ProductControllerInstance.AddProduct)     // 新增產品
	router.POST("/reducestock", controllers.ProductControllerInstance.AddProduct)  // 新增產品
}
