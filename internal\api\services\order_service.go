package services

import (
	"errors"
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/errs"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/types"

	"gorm.io/gorm"
)

// OrderService 負責所有與訂單相關的業務邏輯
type OrderService struct{}

var OrderServiceInstance = &OrderService{}

func (s *OrderService) GetOrder(orderCode string) (*types.Order, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	var order types.Order
	// 查詢 orders 表
	queryOrder := `SELECT id, order_code, order_date, plan_delivery_date, order_status FROM orders WHERE order_code = $1`
	if err := db.Raw(queryOrder, orderCode).Scan(&order).Error; err != nil {
		return nil, err
	}

	// 查詢 order_details 表（關聯條件：order_id）
	var details []types.OrderDetail
	queryDetails := `SELECT id, order_id, product_id, product_quantity, order_part_status, shipped_quantity FROM order_details WHERE order_id = $1`
	if err := db.Raw(queryDetails, order.ID).Scan(&details).Error; err != nil {
		return nil, err
	}

	// 手動設定 Items
	order.Items = details

	return &order, nil
}

func (s *OrderService) GetAllOrders() ([]types.Order, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	var orders []types.Order

	// 查詢所有 orders
	queryOrders := `SELECT id, order_code, order_date, plan_delivery_date, order_status FROM orders`
	if err := db.Raw(queryOrders).Scan(&orders).Error; err != nil {
		return nil, err
	}

	// 針對每一筆 order，查詢對應的 order_details
	for i := range orders {
		var details []types.OrderDetail
		queryDetails := `SELECT id, order_id, product_id, product_quantity, order_part_status, shipped_quantity FROM order_details WHERE order_id = $1`
		if err := db.Raw(queryDetails, orders[i].ID).Scan(&details).Error; err != nil {
			return nil, err
		}
		orders[i].Items = details
	}

	return orders, nil
}

func (s *OrderService) AddOrder(req *request.AddOrderRequest) error {
	db, err := database.DatabaseConnection()
	if err != nil {
		return err
	}

	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	var lastCode string
	err = db.Raw(`SELECT order_code FROM orders ORDER BY order_code DESC LIMIT 1`).Scan(&lastCode).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		fmt.Println("Failed to get last order_code:", err)
		return err
	}
	newCode := generateNextCode("O", lastCode)

	order := types.Order{
		OrderCode:        newCode,
		OrderDate:        req.OrderDate,
		PlanDeliveryDate: req.PlanDeliveryDate,
		OrderStatus:      req.OrderStatus,
	}

	// 使用 Raw SQL 插入 order
	insertOrderQuery := `INSERT INTO orders (order_code, order_date, plan_delivery_date, order_status) VALUES ($1, $2, $3, $4) RETURNING id`
	if err := tx.Raw(insertOrderQuery, order.OrderCode, order.OrderDate, order.PlanDeliveryDate, order.OrderStatus).Scan(&order.ID).Error; err != nil {
		tx.Rollback()
		return err
	}

	for _, item := range req.Items {
		var count int64
		// 使用 Raw SQL 檢查 product
		checkProductQuery := `SELECT COUNT(*) FROM products WHERE id = $1`
		if err := tx.Raw(checkProductQuery, item.ProductID).Scan(&count).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to check product: %v", err)
		}
		if count == 0 {
			tx.Rollback()
			return &errs.ErrProductNotExists{ProductID: item.ProductID}
		}

		detail := types.OrderDetail{
			OrderID:         order.ID,
			ProductID:       item.ProductID,
			ProductQuantity: item.ProductQuantity,
			OrderPartStatus: item.OrderPartStatus,
			ShippedQuantity: 0,
		}

		// 使用 Raw SQL 插入 order_detail
		insertDetailQuery := `INSERT INTO order_details (order_id, product_id, product_quantity, order_part_status, shipped_quantity) VALUES ($1, $2, $3, $4, $5)`
		if err := tx.Exec(insertDetailQuery, detail.OrderID, detail.ProductID, detail.ProductQuantity, detail.OrderPartStatus, detail.ShippedQuantity).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to insert order detail: %v", err)
		}
	}

	return tx.Commit().Error
}
func (s *OrderService) UpdateOrder(req *request.UpdateOrderRequest) error {

	fmt.Printf("Payload received: %+v\n", req)

	db, err := database.DatabaseConnection()
	if err != nil {
		return err
	}

	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 1. 更新訂單本身
	var order types.Order
	// 查找訂單
	findOrderQuery := `SELECT id, order_code, order_date, plan_delivery_date, order_status FROM orders WHERE id = $1`
	if err := tx.Raw(findOrderQuery, req.OrderID).Scan(&order).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &errs.OrderNotExistsError{OrderID: req.OrderID}
		}
		return fmt.Errorf("failed to find order: %v", err)
	}

	// 更新訂單
	updateOrderQuery := `UPDATE orders SET order_date = $1, plan_delivery_date = $2, order_status = $3 WHERE id = $4`
	if err := tx.Exec(updateOrderQuery, req.OrderDate, req.PlanDeliveryDate, req.OrderStatus, req.OrderID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update order: %v", err)
	}

	// 2. 刪除指定 items
	for _, item := range req.DeletedItems {
		deleteDetailQuery := `DELETE FROM order_details WHERE order_id = $1 AND product_id = $2`
		if err := tx.Exec(deleteDetailQuery, req.OrderID, item.ProductID).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to delete order detail for product %d: %v", item.ProductID, err)
		}
	}

	// 3. 更新現有 items
	for _, item := range req.UpdatedItems {
		// 查找 order_detail
		var detail types.OrderDetail
		findDetailQuery := `SELECT id, order_id, product_id, product_quantity, order_part_status, shipped_quantity FROM order_details WHERE id = $1`
		if err := tx.Raw(findDetailQuery, item.OrderDetailID).Scan(&detail).Error; err != nil {
			tx.Rollback()
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("order detail not found: ID=%d", item.OrderDetailID)
			}
			return fmt.Errorf("failed to find order detail: %v", err)
		}

		// 更新 order_detail
		updateDetailQuery := `UPDATE order_details SET product_quantity = $1, order_part_status = $2 WHERE id = $3`
		if err := tx.Exec(updateDetailQuery, item.ProductQuantity, item.OrderPartStatus, item.OrderDetailID).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update order detail: %v", err)
		}
	}

	// 4. 新增 items
	for _, item := range req.NewItems {
		// 檢查 products 表中是否存在這個產品
		var count int64
		checkProductQuery := `SELECT COUNT(*) FROM products WHERE id = $1`
		if err := tx.Raw(checkProductQuery, item.ProductID).Scan(&count).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to check product: %v", err)
		}
		if count == 0 {
			tx.Rollback()
			return &errs.ErrProductNotExists{ProductID: item.ProductID}
		}

		// 檢查 order_details 裡這筆訂單是否已經有這個產品
		var exists int // Use int for SCAN, 0 for false, 1 for true
		checkExistsQuery := `SELECT 1 FROM order_details WHERE order_id = $1 AND product_id = $2 LIMIT 1`
		err := tx.Raw(checkExistsQuery, order.ID, item.ProductID).Scan(&exists).Error

		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) { // gorm.ErrRecordNotFound means it does not exist, which is fine
			tx.Rollback()
			return fmt.Errorf("failed to check if product already exists in order details: %v", err)
		}
		if exists == 1 { // If exists is 1, it means a record was found
			tx.Rollback()
			return &errs.ErrProductExists{ProductID: item.ProductID}
		}

		// 插入新的 OrderDetail
		insertNewDetailQuery := `INSERT INTO order_details (order_id, product_id, product_quantity, order_part_status, shipped_quantity) VALUES ($1, $2, $3, $4, $5)`
		if err := tx.Exec(insertNewDetailQuery, order.ID, item.ProductID, item.ProductQuantity, item.OrderPartStatus, 0).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to insert new order detail: %v", err)
		}
	}
	return tx.Commit().Error
}
