package controllers

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/errs"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"

	"github.com/gin-gonic/gin"
)

type ProductController struct {
	*BaseController
}

var ProductControllerInstance = &ProductController{}

// 取得所有產品
func (p *ProductController) GetProducts(ctx *gin.Context) {

	productCode := getProductCode(ctx)

	var (
		result interface{}
		err    error
	)

	if productCode == "" {
		result, err = services.ProductServiceInstance.GetAllProducts()
	} else {
		result, err = services.ProductServiceInstance.GetProduct(productCode)
	}

	if err != nil {
		log.Printf("Failed to get products: %v", err)
		response.FailWithMessage(ctx, "Failed to get products")
		return
	}
	response.OkWithData(ctx, result)
}

// 新增產品
func (p *ProductController) AddProduct(ctx *gin.Context) {

	var product request.AddProductRequest

	if err := ctx.ShouldBindJSON(&product); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body")
		return
	}

	err := services.ProductServiceInstance.AddProduct(&product)
	if err != nil {
		var notFoundErr *errs.ErrProductNotExists
		if errors.As(err, &notFoundErr) {
			response.OkWithMessage(ctx, fmt.Sprintf("product with ID %d already exists, skipping insert.", notFoundErr.ProductID))
			return
		}

		response.FailWithMessage(ctx, "Failed to add product")
		return
	}

	response.OkWithMessage(ctx, "Product added successfully")
}

func (p *ProductController) AddStock(ctx *gin.Context) {

	var product request.StockRequest

	if err := ctx.ShouldBindJSON(&product); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body")
		return
	}

	err := services.ProductServiceInstance.AddStock(&product)
	if err != nil {
		response.FailWithMessage(ctx, "Failed to add stock")
		return
	}

	response.OkWithMessage(ctx, "Product added successfully")
}

func (p *ProductController) ReduceStock(ctx *gin.Context) {

	var product request.StockRequest

	if err := ctx.ShouldBindJSON(&product); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body")
		return
	}

	err := services.ProductServiceInstance.ReduceStock(&product)
	if err != nil {
		response.FailWithMessage(ctx, "Failed to add stock")
		return
	}

	response.OkWithMessage(ctx, "Product added successfully")
}
