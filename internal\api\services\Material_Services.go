package services

import (
	"errors"
	"fmt"
	"time"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/errs"
	"web-api/internal/pkg/models/types"

	"gorm.io/gorm"
)

type MaterialServices struct {
	*BaseService
}

var Material = &MaterialServices{}

func (s *MaterialServices) GetAllMaterials(pfcModel *types.Material) ([]types.Material, error) {
	var res []types.Material
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	query := `
	SELECT 
	*
	FROM MATERIALS
	`
	err = db.Raw(query).Scan(&res).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	dbInstance.Close()

	return res, nil
}

func (s *MaterialServices) AddMaterial(material *types.Material) error {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return err
	}

	// 檢查是否已存在相同 material
	var existing types.Material
	err = db.Where("material_name = ? AND supplier = ? AND unit_cost = ?", material.MaterialName, material.Supplier, material.UnitCost).First(&existing).Error
	if err == nil {
		return errs.ErrMaterialExists
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		fmt.Println("Query error:", err)
		return err
	}

	// 取得最後 material_code 排序，自己產生下一個 material_code
	var lastCode string
	err = db.Raw(`SELECT material_code FROM materials ORDER BY material_code DESC LIMIT 1`).Scan(&lastCode).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		fmt.Println("Failed to get last material_code:", err)
		return err
	}

	newCode := generateNextCode("M", lastCode) // 你要自己寫這個函數邏輯
	material.MaterialCode = newCode
	material.CreatedAt = time.Now()

	// 新增資料
	err = db.Create(material).Error
	if err != nil {
		fmt.Println("Insert error:", err)
		return err
	}

	fmt.Println("Material inserted:", material.MaterialCode)
	return nil
}
