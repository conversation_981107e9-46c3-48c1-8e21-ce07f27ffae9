package types

type BOMEntry struct {
	BOMCode          string  `json:"bomCode"`
	MaterialCode     string  `json:"materialCode"`
	MaterialQuantity float64 `json:"materialQuantity"`
}
type BOMEntryDetail struct {
	MaterialCode     string  `json:"materialCode"`
	MaterialName     string  `gorm:"column:material_name" json:"MaterialName"`
	MaterialQuantity float64 `json:"materialQuantity"`
	Supplier         string  `gorm:"column:supplier" json:"Supplier"`
	UnitCost         float64 `gorm:"column:unit_cost" json:"UnitCost"`
	Unit             string  `gorm:"column:unit" json:"Unit"`
}
