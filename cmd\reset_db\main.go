package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"web-api/internal/pkg/config"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"

	"gorm.io/gorm"
)

func main() {
	fmt.Println("🚀 ERP Database Reset & Seed Tool")
	fmt.Println("==================================")

	// Set ENV if not set
	if os.Getenv("ENV") == "" {
		os.Setenv("ENV", "dev")
	}

	// Load config first
	fmt.Println("⚙️  Loading configuration...")
	err := config.Setup("data/config.yml")
	if err != nil {
		log.Fatal("❌ Failed to setup config:", err)
	}
	_ = config.LoadFileENV() // Load environment variables

	// Setup database connection
	fmt.Println("📡 Connecting to database...")
	err = database.Setup()
	if err != nil {
		log.Fatal("❌ Failed to setup database:", err)
	}

	db := database.GetDB()
	if db == nil {
		log.Fatal("❌ Database connection is nil")
	}
	fmt.Println("✅ Database connected successfully")

	// Step 1: Clean all data
	fmt.Println("\n🗑️  Step 1: Cleaning existing data...")
	if err := cleanAllData(db); err != nil {
		log.Fatal("❌ Failed to clean data:", err)
	}

	// Step 2: Create fresh tables
	fmt.Println("\n🏗️  Step 2: Creating fresh tables...")
	if err := createFreshTables(db); err != nil {
		log.Fatal("❌ Failed to create tables:", err)
	}

	// Step 3: Seed with realistic data
	fmt.Println("\n🌱 Step 3: Seeding with fresh data...")
	if err := seedRealisticData(db); err != nil {
		log.Fatal("❌ Failed to seed data:", err)
	}

	fmt.Println("\n🎉 Database reset completed successfully!")
	fmt.Println("📊 Summary:")
	printDataSummary(db)
}

func cleanAllData(db *gorm.DB) error {
	// Delete data in correct order (respecting foreign keys)
	tables := []string{
		"order_details",
		"orders",
		"product_movements",
		"product_inventories",
		"product_bom", // Add this table that was causing FK constraint issues
		"products",
		"materials",
	}

	for _, table := range tables {
		result := db.Exec(fmt.Sprintf("DELETE FROM %s", table))
		if result.Error != nil {
			fmt.Printf("⚠️  Warning: Could not clean table %s: %v\n", table, result.Error)
		} else {
			fmt.Printf("✅ Cleaned table: %s (%d rows deleted)\n", table, result.RowsAffected)
		}
	}

	// Reset sequences for PostgreSQL
	sequences := []string{
		"materials_id_seq",
		"products_id_seq",
		"product_inventories_id_seq",
		"product_movements_id_seq",
		"orders_id_seq",
		"order_details_id_seq",
		"product_bom_id_seq",
	}

	for _, seq := range sequences {
		result := db.Exec(fmt.Sprintf("ALTER SEQUENCE IF EXISTS %s RESTART WITH 1", seq))
		if result.Error != nil {
			fmt.Printf("⚠️  Warning: Could not reset sequence %s: %v\n", seq, result.Error)
		} else {
			fmt.Printf("✅ Reset sequence: %s\n", seq)
		}
	}

	return nil
}

func createFreshTables(db *gorm.DB) error {
	// Auto migrate all models
	err := db.AutoMigrate(
		&types.Material{},
		&types.Product{},
		&types.ProductInventory{},
		&types.ProductMovement{},
		&types.Order{},
		&types.OrderDetail{},
	)

	if err != nil {
		return fmt.Errorf("failed to migrate tables: %v", err)
	}

	fmt.Println("✅ All tables created/updated successfully")
	return nil
}

func seedRealisticData(db *gorm.DB) error {
	// 1. Seed Materials
	fmt.Println("📦 Creating materials...")
	materials := []types.Material{
		{MaterialCode: "MAT001", MaterialName: "Nhựa ABS cao cấp", Supplier: "Nhà cung cấp Việt Nam", UnitCost: 28000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "MAT002", MaterialName: "Nhựa PP trong suốt", Supplier: "Công ty Hóa chất Á Châu", UnitCost: 24000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "MAT003", MaterialName: "Nhựa PE chất lượng cao", Supplier: "Tập đoàn Petro", UnitCost: 21000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "MAT004", MaterialName: "Nhựa PVC dẻo", Supplier: "Công ty Nhựa Đông Nam Á", UnitCost: 19000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "MAT005", MaterialName: "Nhựa PS chống tĩnh điện", Supplier: "Nhà máy Hóa chất Bình Dương", UnitCost: 26000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
	}

	for _, material := range materials {
		if err := db.Create(&material).Error; err != nil {
			return fmt.Errorf("failed to create material %s: %v", material.MaterialCode, err)
		}
	}
	fmt.Printf("✅ Created %d materials\n", len(materials))

	// 2. Seed Products with inventory
	fmt.Println("🏭 Creating products...")
	productsData := []struct {
		Product   types.Product
		Inventory float64
	}{
		{types.Product{ProductCode: "PRD001", ProductName: "Hộp cơm 3 ngăn cao cấp", ProductWeight: 0.45, InjectionTime: 85.5, Color: "Trắng sữa", UnitPrice: 45000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 500},
		{types.Product{ProductCode: "PRD002", ProductName: "Chai nước 1.5L thể thao", ProductWeight: 0.35, InjectionTime: 70.0, Color: "Xanh dương", UnitPrice: 32000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 750},
		{types.Product{ProductCode: "PRD003", ProductName: "Ly cà phê 350ml", ProductWeight: 0.12, InjectionTime: 40.0, Color: "Nâu cafe", UnitPrice: 18000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 1200},
		{types.Product{ProductCode: "PRD004", ProductName: "Thùng đựng 50L có nắp", ProductWeight: 2.1, InjectionTime: 150.0, Color: "Xanh lá", UnitPrice: 125000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 200},
		{types.Product{ProductCode: "PRD005", ProductName: "Hộp bảo quản thực phẩm", ProductWeight: 0.28, InjectionTime: 55.5, Color: "Trong suốt", UnitPrice: 28000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 800},
		{types.Product{ProductCode: "PRD006", ProductName: "Chai dầu ăn 1L", ProductWeight: 0.18, InjectionTime: 48.0, Color: "Vàng nhạt", UnitPrice: 22000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 600},
		{types.Product{ProductCode: "PRD007", ProductName: "Hộp đựng bánh kẹo", ProductWeight: 0.15, InjectionTime: 42.5, Color: "Hồng pastel", UnitPrice: 16000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 900},
		{types.Product{ProductCode: "PRD008", ProductName: "Thùng rác 20L có bánh xe", ProductWeight: 1.8, InjectionTime: 135.0, Color: "Xám đen", UnitPrice: 95000, CreatedAt: time.Now(), UpdatedAt: time.Now()}, 150},
	}

	for _, data := range productsData {
		// Create product
		if err := db.Create(&data.Product).Error; err != nil {
			return fmt.Errorf("failed to create product %s: %v", data.Product.ProductCode, err)
		}

		// Create inventory
		inventory := types.ProductInventory{
			ProductID: data.Product.ID,
			Quantity:  data.Inventory,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if err := db.Create(&inventory).Error; err != nil {
			return fmt.Errorf("failed to create inventory for %s: %v", data.Product.ProductCode, err)
		}

		// Create initial movement
		movement := types.ProductMovement{
			ProductID:    data.Product.ID,
			Quantity:     data.Inventory,
			MovementType: "INIT",
			Note:         fmt.Sprintf("Khởi tạo tồn kho cho %s", data.Product.ProductName),
			CreatedAt:    time.Now(),
		}
		if err := db.Create(&movement).Error; err != nil {
			return fmt.Errorf("failed to create movement for %s: %v", data.Product.ProductCode, err)
		}
	}
	fmt.Printf("✅ Created %d products with inventory\n", len(productsData))

	// 3. Seed Orders
	fmt.Println("📋 Creating orders...")
	ordersData := []struct {
		Order   types.Order
		Details []types.OrderDetail
	}{
		{
			types.Order{OrderCode: "ORD001", OrderDate: time.Now().AddDate(0, 0, -15), PlanDeliveryDate: time.Now().AddDate(0, 0, 5), OrderStatus: "PROCESSING", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			[]types.OrderDetail{
				{ProductID: 1, ProductQuantity: 100, OrderPartStatus: "IN_PROGRESS", ShippedQuantity: 30, CreatedAt: time.Now()},
				{ProductID: 3, ProductQuantity: 200, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
			},
		},
		{
			types.Order{OrderCode: "ORD002", OrderDate: time.Now().AddDate(0, 0, -12), PlanDeliveryDate: time.Now().AddDate(0, 0, 8), OrderStatus: "PENDING", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			[]types.OrderDetail{
				{ProductID: 2, ProductQuantity: 150, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
				{ProductID: 5, ProductQuantity: 80, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
			},
		},
		{
			types.Order{OrderCode: "ORD003", OrderDate: time.Now().AddDate(0, 0, -8), PlanDeliveryDate: time.Now().AddDate(0, 0, 12), OrderStatus: "COMPLETED", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			[]types.OrderDetail{
				{ProductID: 4, ProductQuantity: 25, OrderPartStatus: "COMPLETED", ShippedQuantity: 25, CreatedAt: time.Now()},
				{ProductID: 6, ProductQuantity: 120, OrderPartStatus: "COMPLETED", ShippedQuantity: 120, CreatedAt: time.Now()},
				{ProductID: 7, ProductQuantity: 300, OrderPartStatus: "COMPLETED", ShippedQuantity: 300, CreatedAt: time.Now()},
			},
		},
		{
			types.Order{OrderCode: "ORD004", OrderDate: time.Now().AddDate(0, 0, -5), PlanDeliveryDate: time.Now().AddDate(0, 0, 15), OrderStatus: "IN_PROGRESS", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			[]types.OrderDetail{
				{ProductID: 1, ProductQuantity: 75, OrderPartStatus: "PROCESSING", ShippedQuantity: 20, CreatedAt: time.Now()},
				{ProductID: 8, ProductQuantity: 40, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
			},
		},
		{
			types.Order{OrderCode: "ORD005", OrderDate: time.Now().AddDate(0, 0, -2), PlanDeliveryDate: time.Now().AddDate(0, 0, 18), OrderStatus: "PENDING", CreatedAt: time.Now(), UpdatedAt: time.Now()},
			[]types.OrderDetail{
				{ProductID: 2, ProductQuantity: 200, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
				{ProductID: 3, ProductQuantity: 500, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
				{ProductID: 5, ProductQuantity: 150, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
			},
		},
	}

	for _, data := range ordersData {
		// Create order
		if err := db.Create(&data.Order).Error; err != nil {
			return fmt.Errorf("failed to create order %s: %v", data.Order.OrderCode, err)
		}

		// Create order details
		for _, detail := range data.Details {
			detail.OrderID = data.Order.ID
			if err := db.Create(&detail).Error; err != nil {
				return fmt.Errorf("failed to create detail for order %s: %v", data.Order.OrderCode, err)
			}
		}
	}
	fmt.Printf("✅ Created %d orders with details\n", len(ordersData))

	return nil
}

func printDataSummary(db *gorm.DB) {
	var count int64

	db.Model(&types.Material{}).Count(&count)
	fmt.Printf("📦 Materials: %d\n", count)

	db.Model(&types.Product{}).Count(&count)
	fmt.Printf("🏭 Products: %d\n", count)

	db.Model(&types.Order{}).Count(&count)
	fmt.Printf("📋 Orders: %d\n", count)

	db.Model(&types.OrderDetail{}).Count(&count)
	fmt.Printf("📝 Order Details: %d\n", count)
}
