FROM golang:1.19.4-alpine AS build_base

ENV CGO_ENABLED=1
ENV GO111MODULE=on
RUN apk add --no-cache git gcc g++

WORKDIR /src

# Copy go mod trước để cache
COPY go.mod .
COPY go.sum .
RUN go mod download

# Copy toàn bộ source + file env
COPY . .
COPY .env.production .env.production

# Build Go app
RUN go build -o ./out/app ./cmd/main.go

# === Runtime image ===
FROM alpine:3.17.0
RUN apk add ca-certificates

WORKDIR /app

COPY --from=build_base /src/out/app /app/webapi
COPY --from=build_base /src/data /app/data
COPY --from=build_base /src/log /app/log
COPY --from=build_base /src/.env.production /app/.env.production

RUN chmod +x webapi

EXPOSE 8081

ENTRYPOINT ["./webapi"]

