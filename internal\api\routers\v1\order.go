package router_v1

import (
	"web-api/internal/api/controllers"

	"github.com/gin-gonic/gin"
)

func OrderRouter(router *gin.RouterGroup) {
	router.GET("/:orderCode", controllers.OrderControllerInstance.GetOrders)   // 根據訂單編號獲取訂單
	router.GET("", controllers.OrderControllerInstance.GetOrders)              // 獲取訂單
	router.POST("", controllers.OrderControllerInstance.AddOrder)              // 新增訂單
	router.PUT("/:orderCode", controllers.OrderControllerInstance.UpdateOrder) // 修改訂單
}
