build:
	go build -o bin/web-api cmd/main.go

run:
	go run cmd/main.go

format:
	go fmt web-api/...

build-docker:
	docker build . -t web-api

run-docker:
	docker run -itd --name web-api --restart always -p 8081:8081 web-api

exec-docker:
	docker exec -it web-api sh

# Testing commands
test:
	go test ./tests/... -v

test-controllers:
	go test ./tests/controllers/... -v

test-integration:
	INTEGRATION_TESTS=true go test ./tests/integration/... -v

test-coverage:
	go test ./tests/... -v -coverprofile=coverage.out
	go tool cover -html=coverage.out -o coverage.html

test-race:
	go test ./tests/... -v -race

test-bench:
	go test ./tests/... -v -bench=.

test-clean:
	go clean -testcache
	rm -f coverage.out coverage.html test.db

# Run all tests
test-all: test-clean test test-integration test-coverage

# Quick test (unit tests only)
test-quick:
	go test ./tests/controllers/... -v -short