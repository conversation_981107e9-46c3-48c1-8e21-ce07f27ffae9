package main

import (
	"fmt"
	"log"
	"time"

	"web-api/internal/pkg/config"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"

	"gorm.io/gorm"
)

func main() {
	fmt.Println("🗑️  Starting database reset and seeding...")

	// Setup database connection
	err := database.Setup()
	if err != nil {
		log.Fatal("Failed to setup database:", err)
	}

	db := database.GetDB()
	if db == nil {
		log.Fatal("Database connection is nil")
	}

	// Step 1: Drop all existing data
	fmt.Println("📋 Step 1: Dropping all existing data...")
	if err := dropAllData(db); err != nil {
		log.Fatal("Failed to drop data:", err)
	}

	// Step 2: Create tables (migration)
	fmt.Println("🏗️  Step 2: Creating tables...")
	if err := createTables(db); err != nil {
		log.Fatal("Failed to create tables:", err)
	}

	// Step 3: Seed fake data
	fmt.Println("🌱 Step 3: Seeding fake data...")
	if err := seedFakeData(db); err != nil {
		log.Fatal("Failed to seed data:", err)
	}

	fmt.Println("✅ Database reset and seeding completed successfully!")
}

func dropAllData(db *gorm.DB) error {
	// Drop tables in correct order (reverse of foreign key dependencies)
	tables := []string{
		"order_details",
		"orders", 
		"product_movements",
		"product_inventories",
		"products",
		"bom_details",
		"boms",
		"materials",
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table)).Error; err != nil {
			fmt.Printf("Warning: Failed to drop table %s: %v\n", table, err)
		} else {
			fmt.Printf("✅ Dropped table: %s\n", table)
		}
	}

	return nil
}

func createTables(db *gorm.DB) error {
	// Auto migrate all models
	err := db.AutoMigrate(
		&types.Material{},
		&types.Product{},
		&types.ProductInventory{},
		&types.ProductMovement{},
		&types.Order{},
		&types.OrderDetail{},
	)

	if err != nil {
		return fmt.Errorf("failed to migrate tables: %v", err)
	}

	fmt.Println("✅ All tables created successfully")
	return nil
}

func seedFakeData(db *gorm.DB) error {
	// Seed Materials
	fmt.Println("📦 Seeding materials...")
	materials := []types.Material{
		{MaterialCode: "M001", MaterialName: "Nhựa ABS", Supplier: "Công ty A", UnitCost: 25000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "M002", MaterialName: "Nhựa PP", Supplier: "Công ty B", UnitCost: 22000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "M003", MaterialName: "Nhựa PE", Supplier: "Công ty C", UnitCost: 20000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "M004", MaterialName: "Nhựa PVC", Supplier: "Công ty D", UnitCost: 18000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
		{MaterialCode: "M005", MaterialName: "Nhựa PS", Supplier: "Công ty E", UnitCost: 23000, Unit: "kg", CreatedAt: time.Now(), Deleted: false},
	}

	for _, material := range materials {
		if err := db.Create(&material).Error; err != nil {
			return fmt.Errorf("failed to create material %s: %v", material.MaterialCode, err)
		}
	}
	fmt.Printf("✅ Created %d materials\n", len(materials))

	// Seed Products
	fmt.Println("🏭 Seeding products...")
	products := []types.Product{
		{ProductCode: "P001", ProductName: "Hộp nhựa vuông 500ml", ProductWeight: 0.15, InjectionTime: 45.5, Color: "Trắng", UnitPrice: 15000, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ProductCode: "P002", ProductName: "Chai nhựa tròn 1L", ProductWeight: 0.25, InjectionTime: 60.0, Color: "Xanh", UnitPrice: 25000, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ProductCode: "P003", ProductName: "Hộp cơm 3 ngăn", ProductWeight: 0.35, InjectionTime: 75.5, Color: "Đỏ", UnitPrice: 35000, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ProductCode: "P004", ProductName: "Ly nhựa 250ml", ProductWeight: 0.08, InjectionTime: 30.0, Color: "Vàng", UnitPrice: 8000, CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{ProductCode: "P005", ProductName: "Thùng nhựa 20L", ProductWeight: 1.2, InjectionTime: 120.0, Color: "Đen", UnitPrice: 85000, CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	for _, product := range products {
		if err := db.Create(&product).Error; err != nil {
			return fmt.Errorf("failed to create product %s: %v", product.ProductCode, err)
		}

		// Create inventory for each product
		inventory := types.ProductInventory{
			ProductID: product.ID,
			Quantity:  float64(100 + (product.ID * 50)), // Different quantities for each product
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		if err := db.Create(&inventory).Error; err != nil {
			return fmt.Errorf("failed to create inventory for product %s: %v", product.ProductCode, err)
		}

		// Create initial movement
		movement := types.ProductMovement{
			ProductID:    product.ID,
			Quantity:     inventory.Quantity,
			MovementType: "INIT",
			Note:         "Khởi tạo tồn kho ban đầu",
			CreatedAt:    time.Now(),
		}
		if err := db.Create(&movement).Error; err != nil {
			return fmt.Errorf("failed to create movement for product %s: %v", product.ProductCode, err)
		}
	}
	fmt.Printf("✅ Created %d products with inventory\n", len(products))

	// Seed Orders
	fmt.Println("📋 Seeding orders...")
	orders := []types.Order{
		{OrderCode: "O001", OrderDate: time.Now().AddDate(0, 0, -10), PlanDeliveryDate: time.Now().AddDate(0, 0, 5), OrderStatus: "PENDING", CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{OrderCode: "O002", OrderDate: time.Now().AddDate(0, 0, -8), PlanDeliveryDate: time.Now().AddDate(0, 0, 7), OrderStatus: "PROCESSING", CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{OrderCode: "O003", OrderDate: time.Now().AddDate(0, 0, -5), PlanDeliveryDate: time.Now().AddDate(0, 0, 10), OrderStatus: "IN_PROGRESS", CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{OrderCode: "O004", OrderDate: time.Now().AddDate(0, 0, -3), PlanDeliveryDate: time.Now().AddDate(0, 0, 12), OrderStatus: "COMPLETED", CreatedAt: time.Now(), UpdatedAt: time.Now()},
		{OrderCode: "O005", OrderDate: time.Now().AddDate(0, 0, -1), PlanDeliveryDate: time.Now().AddDate(0, 0, 15), OrderStatus: "PENDING", CreatedAt: time.Now(), UpdatedAt: time.Now()},
	}

	for _, order := range orders {
		if err := db.Create(&order).Error; err != nil {
			return fmt.Errorf("failed to create order %s: %v", order.OrderCode, err)
		}

		// Create order details for each order
		orderDetails := []types.OrderDetail{
			{OrderID: order.ID, ProductID: 1, ProductQuantity: 50, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now()},
			{OrderID: order.ID, ProductID: 2, ProductQuantity: 30, OrderPartStatus: "PROCESSING", ShippedQuantity: 10, CreatedAt: time.Now()},
		}

		// Add different products for different orders
		if order.ID%2 == 0 {
			orderDetails = append(orderDetails, types.OrderDetail{
				OrderID: order.ID, ProductID: 3, ProductQuantity: 25, OrderPartStatus: "PENDING", ShippedQuantity: 0, CreatedAt: time.Now(),
			})
		}

		for _, detail := range orderDetails {
			if err := db.Create(&detail).Error; err != nil {
				return fmt.Errorf("failed to create order detail for order %s: %v", order.OrderCode, err)
			}
		}
	}
	fmt.Printf("✅ Created %d orders with details\n", len(orders))

	return nil
}
