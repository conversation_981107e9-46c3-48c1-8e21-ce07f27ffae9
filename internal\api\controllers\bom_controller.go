package controllers

import (
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"

	"github.com/gin-gonic/gin"
)

type BOMController struct {
	*BaseController
}

var BOM = &BOMController{}

// 新增 BOM entries
func (b *BOMController) AddBOMEntries(ctx *gin.Context) {
	var payload request.AddBOMRequest

	if err := ctx.ShouldBindJSON(&payload); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body")
		return
	}

	if payload.ProductCode == "" || len(payload.Entries) == 0 {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Product code and BOM entries are required")
		return
	}

	err := services.BOMService.AddBOMEntries(payload.ProductCode, payload.Entries)
	if err != nil {
		log.Printf("Failed to add BOM entries: %v", err)
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithMessage(ctx, "BOM entries added successfully")
}

// 查詢某個產品的所有 BOM entries
func (b *BOMController) GetProductBOMEntries(ctx *gin.Context) {
	productCode := getProductCode(ctx)
	if productCode == "" {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "productCode is required")
		return
	}

	entries, err := services.BOMService.GetProductBOMEntries(productCode)
	if err != nil {
		log.Printf("Failed to get BOM entries: %v", err)
		response.FailWithMessage(ctx, "Failed to get BOM entries")
		return
	}

	response.OkWithData(ctx, entries)
}

func (b *BOMController) GetProductCost(ctx *gin.Context) {
	productCode := getProductCode(ctx)
	if productCode == "" {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "productCode is required")
		return
	}

	cost, err := services.BOMService.CalculateProductCost(productCode)
	if err != nil {
		log.Printf("Failed to get product cost: %v", err)
		response.FailWithMessage(ctx, "Failed to get product cost")
		return
	}

	response.OkWithData(ctx, cost)
}
