package types

import "time"

type Product struct {
	ID            int64     `json:"id" gorm:"primaryKey;column:id"`
	ProductCode   string    `json:"productCode" gorm:"column:product_code"`
	ProductName   string    `json:"productName" gorm:"column:product_name"`
	ProductWeight float64   `json:"productWeight" gorm:"column:product_weight"`
	InjectionTime float64   `json:"injectionTime" gorm:"column:injection_time"`
	Color         string    `json:"color" gorm:"column:color"`
	UnitPrice     float64   `json:"unitPrice" gorm:"column:unit_price"`
	CreatedAt     time.Time `json:"createdAt" gorm:"column:created_at"`
	UpdatedAt     time.Time `json:"updatedAt" gorm:"column:updated_at"`
}
type ProductInventory struct {
	ID        int64     `json:"id" gorm:"primaryKey;column:id"`
	ProductID int64     `json:"productId" gorm:"column:product_id;not null"` // FK to products.id
	Quantity  float64   `json:"quantity" gorm:"column:quantity"`             // 現有庫存
	CreatedAt time.Time `json:"createdAt" gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"column:updated_at;autoUpdateTime"`

	Product Product `gorm:"foreignKey:ProductID"`
}
type ProductMovement struct {
	ID             int64     `json:"id" gorm:"primaryKey;column:id"`
	ProductID      int64     `json:"productId" gorm:"column:product_id;not null"`             // FK to products.id
	Quantity       float64   `json:"quantity" gorm:"column:quantity"`                         // 可正（入庫）可負（出庫）
	MovementType   string    `json:"movementType" gorm:"column:movement_type"`                // "IN", "OUT", "ADJUST"
	RelatedOrderID *int64    `json:"relatedOrderId,omitempty" gorm:"column:related_order_id"` // 可為 null
	Note           string    `json:"note" gorm:"column:note"`                                 // 說明備註
	CreatedAt      time.Time `json:"createdAt" gorm:"column:created_at;autoCreateTime"`

	Product Product `gorm:"foreignKey:ProductID"`
}
