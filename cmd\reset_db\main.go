package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"web-api/internal/pkg/config"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"

	"gorm.io/gorm"
)

func main() {
	fmt.Println("🚀 ERP Database Reset & Seed Tool")
	fmt.Println("==================================")

	// Set ENV if not set
	if os.Getenv("ENV") == "" {
		os.Setenv("ENV", "dev")
	}

	// Load config first
	fmt.Println("⚙️  Loading configuration...")
	err := config.Setup("data/config.yml")
	if err != nil {
		log.Fatal("❌ Failed to setup config:", err)
	}
	_ = config.LoadFileENV() // Load environment variables

	// Setup database connection
	fmt.Println("📡 Connecting to database...")
	err = database.Setup()
	if err != nil {
		log.Fatal("❌ Failed to setup database:", err)
	}

	db := database.GetDB()
	if db == nil {
		log.Fatal("❌ Database connection is nil")
	}
	fmt.Println("✅ Database connected successfully")

	// Step 1: Clean all data
	fmt.Println("\n🗑️  Step 1: Cleaning existing data...")
	if err := cleanAllData(db); err != nil {
		log.Fatal("❌ Failed to clean data:", err)
	}

	// Step 2: Create fresh tables
	fmt.Println("\n🏗️  Step 2: Creating fresh tables...")
	if err := createFreshTables(db); err != nil {
		log.Fatal("❌ Failed to create tables:", err)
	}

	// Step 3: Seed with realistic data
	fmt.Println("\n🌱 Step 3: Seeding with fresh data...")
	if err := seedRealisticData(db); err != nil {
		log.Fatal("❌ Failed to seed data:", err)
	}

	fmt.Println("\n🎉 Database reset completed successfully!")
	fmt.Println("📊 Summary:")
	printDataSummary(db)
}

func cleanAllData(db *gorm.DB) error {
	// Delete data in correct order (respecting foreign keys)
	tables := []string{
		"order_details",
		"orders",
		"product_movements",
		"product_inventories",
		"product_bom", // Add this table that was causing FK constraint issues
		"products",
		"materials",
	}

	for _, table := range tables {
		result := db.Exec(fmt.Sprintf("DELETE FROM %s", table))
		if result.Error != nil {
			fmt.Printf("⚠️  Warning: Could not clean table %s: %v\n", table, result.Error)
		} else {
			fmt.Printf("✅ Cleaned table: %s (%d rows deleted)\n", table, result.RowsAffected)
		}
	}

	// Reset sequences for PostgreSQL
	sequences := []string{
		"materials_id_seq",
		"products_id_seq",
		"product_inventories_id_seq",
		"product_movements_id_seq",
		"orders_id_seq",
		"order_details_id_seq",
		"product_bom_id_seq",
	}

	for _, seq := range sequences {
		result := db.Exec(fmt.Sprintf("ALTER SEQUENCE IF EXISTS %s RESTART WITH 1", seq))
		if result.Error != nil {
			fmt.Printf("⚠️  Warning: Could not reset sequence %s: %v\n", seq, result.Error)
		} else {
			fmt.Printf("✅ Reset sequence: %s\n", seq)
		}
	}

	return nil
}

func createFreshTables(db *gorm.DB) error {
	// Auto migrate all models
	err := db.AutoMigrate(
		&types.Material{},
		&types.Product{},
		&types.ProductInventory{},
		&types.ProductMovement{},
		&types.Order{},
		&types.OrderDetail{},
	)

	if err != nil {
		return fmt.Errorf("failed to migrate tables: %v", err)
	}

	fmt.Println("✅ All tables created/updated successfully")
	return nil
}

func seedRealisticData(db *gorm.DB) error {
	// 1. Seed Materials (200 materials)
	fmt.Println("📦 Creating materials...")
	materialTypes := []string{"ABS", "PP", "PE", "PVC", "PS", "PC", "PMMA", "POM", "PA", "TPU", "PET", "HDPE", "LDPE", "EVA", "TPE"}
	suppliers := []string{
		"Nhà cung cấp Việt Nam", "Công ty Hóa chất Á Châu", "Tập đoàn Petro",
		"Công ty Nhựa Đông Nam Á", "Nhà máy Hóa chất Bình Dương", "Công ty TNHH Nhựa Sài Gòn",
		"Tập đoàn Hóa chất Hà Nội", "Công ty Nhựa Miền Trung", "Nhà máy Polymer Đà Nẵng",
		"Công ty Nhựa Cao Su Việt Nam", "Tập đoàn Hóa chất Miền Nam", "Công ty Nhựa Bình Thuận",
		"Nhà máy Polymer Hồ Chí Minh", "Công ty Hóa chất Đồng Nai", "Tập đoàn Nhựa Việt",
	}

	var materials []types.Material
	for i := 1; i <= 200; i++ {
		materialType := materialTypes[i%len(materialTypes)]
		supplier := suppliers[i%len(suppliers)]
		material := types.Material{
			MaterialCode: fmt.Sprintf("MAT%03d", i),
			MaterialName: fmt.Sprintf("Nhựa %s loại %d", materialType, (i%5)+1),
			Supplier:     supplier,
			UnitCost:     float64(15000 + (i * 500) + (i%10)*1000), // Varied pricing
			Unit:         "kg",
			CreatedAt:    time.Now().AddDate(0, 0, -i%365), // Spread over past year
			Deleted:      false,
		}
		materials = append(materials, material)
	}

	// Create materials in batches
	if err := db.Create(&materials).Error; err != nil {
		return fmt.Errorf("failed to create materials: %v", err)
	}
	fmt.Printf("✅ Created %d materials\n", len(materials))

	// 2. Seed Products with inventory (500 products)
	fmt.Println("🏭 Creating products...")
	productTypes := []string{
		"Hộp cơm", "Chai nước", "Ly", "Thùng đựng", "Hộp bảo quản", "Chai dầu ăn",
		"Hộp bánh kẹo", "Thùng rác", "Túi nilon", "Khay nhựa", "Bát nhựa", "Đĩa nhựa",
		"Cốc nhựa", "Hộp đựng giày", "Thùng chứa", "Chai xịt", "Hộp đựng đồ chơi",
		"Khay trưng bày", "Hộp công cụ", "Chai sữa tắm", "Hộp đựng thực phẩm", "Chai dầu gội",
		"Thùng đá", "Hộp đựng văn phòng phẩm", "Chai nước rửa chén", "Khay đựng bánh",
		"Hộp đựng quần áo", "Chai xà phòng", "Thùng đựng đồ chơi", "Hộp đựng mỹ phẩm",
	}
	colors := []string{
		"Trắng", "Đen", "Xanh dương", "Xanh lá", "Đỏ", "Vàng", "Hồng", "Tím",
		"Cam", "Nâu", "Xám", "Trong suốt", "Xanh mint", "Hồng pastel", "Vàng nhạt",
		"Xanh navy", "Đỏ đậm", "Vàng cam", "Tím nhạt", "Xanh ngọc", "Hồng đậm",
	}

	var productsData []struct {
		Product   types.Product
		Inventory float64
	}

	for i := 1; i <= 500; i++ {
		productType := productTypes[i%len(productTypes)]
		color := colors[i%len(colors)]

		// Generate varied specifications
		weight := 0.1 + float64(i%20)*0.1 + float64(i%5)*0.05
		injectionTime := 30.0 + float64(i%10)*10.0 + float64(i%3)*5.0
		unitPrice := float64(10000 + (i * 1000) + (i%7)*5000)
		inventory := float64(100 + (i * 10) + (i%8)*50)

		product := types.Product{
			ProductCode:   fmt.Sprintf("PRD%03d", i),
			ProductName:   fmt.Sprintf("%s %s loại %d", productType, color, (i%5)+1),
			ProductWeight: weight,
			InjectionTime: injectionTime,
			Color:         color,
			UnitPrice:     unitPrice,
			CreatedAt:     time.Now().AddDate(0, 0, -i%180), // Spread over past 6 months
			UpdatedAt:     time.Now().AddDate(0, 0, -i%90),  // Updated more recently
		}

		productsData = append(productsData, struct {
			Product   types.Product
			Inventory float64
		}{product, inventory})
	}

	// Create products in batches for better performance
	productBatchSize := 50
	for i := 0; i < len(productsData); i += productBatchSize {
		end := i + productBatchSize
		if end > len(productsData) {
			end = len(productsData)
		}

		batch := productsData[i:end]

		// Create products in this batch
		var products []types.Product
		for _, data := range batch {
			products = append(products, data.Product)
		}

		if err := db.Create(&products).Error; err != nil {
			return fmt.Errorf("failed to create product batch: %v", err)
		}

		// Create inventories and movements for this batch
		var inventories []types.ProductInventory
		var movements []types.ProductMovement

		for j, data := range batch {
			productID := products[j].ID

			inventory := types.ProductInventory{
				ProductID: productID,
				Quantity:  data.Inventory,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}
			inventories = append(inventories, inventory)

			movement := types.ProductMovement{
				ProductID:    productID,
				Quantity:     data.Inventory,
				MovementType: "INIT",
				Note:         fmt.Sprintf("Khởi tạo tồn kho cho %s", data.Product.ProductName),
				CreatedAt:    time.Now(),
			}
			movements = append(movements, movement)
		}

		if err := db.Create(&inventories).Error; err != nil {
			return fmt.Errorf("failed to create inventory batch: %v", err)
		}

		if err := db.Create(&movements).Error; err != nil {
			return fmt.Errorf("failed to create movement batch: %v", err)
		}

		fmt.Printf("✅ Created batch %d-%d products\n", i+1, end)
	}
	fmt.Printf("✅ Created %d products with inventory\n", len(productsData))

	// 3. Seed Orders (300 orders with multiple details each)
	fmt.Println("📋 Creating orders...")
	orderStatuses := []string{"PENDING", "PROCESSING", "IN_PROGRESS", "COMPLETED", "CANCELLED"}
	partStatuses := []string{"PENDING", "PROCESSING", "IN_PROGRESS", "COMPLETED"}

	var ordersData []struct {
		Order   types.Order
		Details []types.OrderDetail
	}

	for i := 1; i <= 300; i++ {
		orderStatus := orderStatuses[i%len(orderStatuses)]

		// Create order
		order := types.Order{
			OrderCode:        fmt.Sprintf("ORD%04d", i),              // 4 digits for more orders
			OrderDate:        time.Now().AddDate(0, 0, -(i%180 + 1)), // Orders from past 6 months
			PlanDeliveryDate: time.Now().AddDate(0, 0, (i%60 + 5)),   // Delivery in next 2 months
			OrderStatus:      orderStatus,
			CreatedAt:        time.Now().AddDate(0, 0, -(i%180 + 1)),
			UpdatedAt:        time.Now().AddDate(0, 0, -(i % 90)),
		}

		// Create 3-8 order details per order (more items per order)
		numDetails := 3 + (i % 6) // 3 to 8 details
		var details []types.OrderDetail

		for j := 0; j < numDetails; j++ {
			productID := int64(((i*5 + j) % 500) + 1) // Distribute across all 500 products
			quantity := float64(50 + (i+j)*10 + (i%5)*20)
			partStatus := partStatuses[j%len(partStatuses)]

			// Set shipped quantity based on status
			var shippedQty int64 = 0
			if partStatus == "COMPLETED" {
				shippedQty = int64(quantity)
			} else if partStatus == "IN_PROGRESS" || partStatus == "PROCESSING" {
				shippedQty = int64(quantity * 0.3) // 30% shipped
			}

			detail := types.OrderDetail{
				ProductID:       productID,
				ProductQuantity: quantity,
				OrderPartStatus: partStatus,
				ShippedQuantity: shippedQty,
				CreatedAt:       time.Now().AddDate(0, 0, -(i%60 + 1)),
			}
			details = append(details, detail)
		}

		ordersData = append(ordersData, struct {
			Order   types.Order
			Details []types.OrderDetail
		}{order, details})
	}

	// Create orders in batches for better performance
	batchSize := 25 // Larger batch size for better performance with more data
	for i := 0; i < len(ordersData); i += batchSize {
		end := i + batchSize
		if end > len(ordersData) {
			end = len(ordersData)
		}

		batch := ordersData[i:end]

		// Create orders in this batch
		var orders []types.Order
		for _, data := range batch {
			orders = append(orders, data.Order)
		}

		if err := db.Create(&orders).Error; err != nil {
			return fmt.Errorf("failed to create order batch: %v", err)
		}

		// Create order details for this batch
		var allDetails []types.OrderDetail
		for j, data := range batch {
			orderID := orders[j].ID
			for _, detail := range data.Details {
				detail.OrderID = orderID
				allDetails = append(allDetails, detail)
			}
		}

		if len(allDetails) > 0 {
			if err := db.Create(&allDetails).Error; err != nil {
				return fmt.Errorf("failed to create order details batch: %v", err)
			}
		}

		fmt.Printf("✅ Created batch %d-%d orders\n", i+1, end)
	}
	fmt.Printf("✅ Created %d orders with details\n", len(ordersData))

	return nil
}

func printDataSummary(db *gorm.DB) {
	var count int64

	db.Model(&types.Material{}).Count(&count)
	fmt.Printf("📦 Materials: %d\n", count)

	db.Model(&types.Product{}).Count(&count)
	fmt.Printf("🏭 Products: %d\n", count)

	db.Model(&types.Order{}).Count(&count)
	fmt.Printf("📋 Orders: %d\n", count)

	db.Model(&types.OrderDetail{}).Count(&count)
	fmt.Printf("📝 Order Details: %d\n", count)
}
