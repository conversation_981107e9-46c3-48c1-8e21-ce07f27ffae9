package services

import (
	"fmt"
	"strconv"
	"strings"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"
)

type CommonService struct {
	*BaseService
}

var Common = &CommonService{}

func (s *CommonService) Login(params *types.User) (any, error) {
	var User []types.User
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return nil, err
	}
	dbInstance, _ := db.DB()
	query := // `
		// 	SELECT TOP 1
		// 		CAST(UserID AS NVARCHAR(36)) AS UserID,
		// 		UserName,
		// 		Password,
		// 		RoleID,
		// 		CreateDate,
		// 		CreatedBy,
		// 		IsActive
		// 	FROM USERS
		// 	WHERE UserName = ? and IsActive = 'true'
		// `
		`
	SELECT 
	user_id,
	user_name,
	password,
	role_id,
	create_date,
	created_by,
	is_active
FROM users
WHERE user_name = $1 AND is_active = TRUE
LIMIT 1;
	`
	err = db.Raw(query, params.UserName).Scan(&User).Error
	if err != nil {
		fmt.Println("Query error:", err)
		return nil, err
	}
	dbInstance.Close()

	if len(User) == 0 {
		return "Account không tồn tại", nil
	}
	if User[0].Password != params.Password {
		return "Không đúng password", nil
	} else {
		Token := User[0].UserID + User[0].RoleID
		User[0].Token = Token
		User[0].Password = ``
		return User, nil
	}

}
func generateNextCode(prefix string, lastID string) string {

	const maxNumber = 99999
	const numberWidth = 5

	if lastID == "" {
		return fmt.Sprintf("%s%0*d", prefix, numberWidth, 1)
	}

	numberPart := strings.TrimPrefix(lastID, prefix)
	num, err := strconv.Atoi(numberPart)
	if err != nil {
		num = 0
	}

	num++
	if num > maxNumber {
		return ""
	}

	return fmt.Sprintf("%s%0*d", prefix, numberWidth, num)
}
