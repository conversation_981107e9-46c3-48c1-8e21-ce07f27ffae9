package controllers

import (
	"fmt"
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/config"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type CommonController struct {
	*BaseController
}

var Common = &CommonController{}

func (c *CommonController) Ping(ctx *gin.Context) {

	env := config.LoadFileENV()
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Connection to "+env.HOST+"  Failed : ", err)
		ctx.JSON(500, gin.H{
			"message": "Connection to " + env.HOST + " Failed",
			"error":   err.Error(),
		})
		return
	}
	dbInstance, _ := db.DB()
	dbInstance.Close()
	ctx.JSON(200, gin.H{
		"message": "Connected to " + env.HOST + " successfully",
	})
}


func (c *CommonController) Login(ctx *gin.Context) {
	var requestParams types.User
	if err := ctx.ShouldBind(&requestParams); err != nil {
		log.Printf("Invalid form data: %v", err)
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid form data")
		return
	}
	result, err := services.Common.Login(&requestParams)
	if err != nil {
		response.FailWithDetailed(ctx, http.StatusInternalServerError, nil, err.Error())
		return
	}

	response.OkWithData(ctx, result)
}
