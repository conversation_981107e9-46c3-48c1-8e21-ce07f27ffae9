package types

import (
	"time"
)

type Material struct {
	ID           int       `gorm:"column:id;primaryKey" json:"ID"`
	MaterialCode string    `gorm:"column:material_code" json:"MaterialCode"`
	MaterialName string    `gorm:"column:material_name" json:"MaterialName"`
	Supplier     string    `gorm:"column:supplier" json:"Supplier"`
	UnitCost     float64   `gorm:"column:unit_cost" json:"UnitCost"`
	Unit         string    `gorm:"column:unit" json:"Unit"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"CreatedAt"`
	Deleted      bool      `gorm:"column:deleted" json:"Deleted"`
}
