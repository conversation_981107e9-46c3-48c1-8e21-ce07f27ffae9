# ERP API Documentation

## Tổng quan
ERP API là một hệ thống quản lý tài nguyên doanh nghiệp được xây dựng bằng Go và Gin framework. API cung cấp các chức năng quản lý vật li<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> (Bill of Materials), và đơn hàng.

## Base URL
```
http://localhost:{port}/api/
```

## Authentication
API sử dụng token-based authentication. Cần login để lấy token trước khi sử dụng các API khác.

## Response Format
Tất cả API responses đều có format chung:

```json
{
  "code": 200,
  "data": {},
  "message": "success"
}
```

## API Endpoints

### 1. Common APIs

#### 1.1 Health Check
**GET** `/ping`

Kiểm tra kết nối database và trạng thái server.

**Response:**
```json
{
  "message": "Connected to {HOST} successfully"
}
```

#### 1.2 Login
**POST** `/v1/login`

Đăng nhập để lấy token xác thực.

**Request Body:**
```json
{
  "UserID": "string",
  "UserName": "string", 
  "Password": "string",
  "RoleID": "string"
}
```

**Response:**
```json
{
  "code": 200,
  "data": {
    "UserID": "string",
    "UserName": "string",
    "RoleID": "string",
    "Token": "string",
    "CreateDate": "string",
    "CreatedBy": "string",
    "IsActive": "string"
  },
  "message": "success"
}
```

### 2. Material APIs

#### 2.1 Get All Materials
**GET** `/v1/material`

Lấy danh sách tất cả vật liệu.

**Response:**
```json
{
  "code": 200,
  "data": [
    {
      "ID": 1,
      "MaterialCode": "MAT001",
      "MaterialName": "Steel Rod",
      "Supplier": "ABC Steel Co.",
      "UnitCost": 15.50,
      "Unit": "kg",
      "CreatedAt": "2024-01-01T00:00:00Z",
      "Deleted": false
    }
  ],
  "message": "success"
}
```

#### 2.2 Add Material
**POST** `/v1/material`

Thêm vật liệu mới.

**Request Body:**
```json
{
  "MaterialCode": "MAT001",
  "MaterialName": "Steel Rod",
  "Supplier": "ABC Steel Co.",
  "UnitCost": 15.50,
  "Unit": "kg"
}
```

**Response:**
```json
{
  "code": 200,
  "data": null,
  "message": "Material added successfully"
}
```

### 3. Product APIs

#### 3.1 Get All Products
**GET** `/v1/product`

Lấy danh sách tất cả sản phẩm.

**Response:**
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "productCode": "PROD001",
      "productName": "Widget A",
      "productWeight": 2.5,
      "injectionTime": 30.0,
      "color": "Red",
      "unitPrice": 100.00,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "message": "success"
}
```

#### 3.2 Get Product by Code
**GET** `/v1/product/{productCode}`

Lấy thông tin sản phẩm theo mã sản phẩm.

**Parameters:**
- `productCode` (path): Mã sản phẩm

**Response:** Tương tự Get All Products nhưng chỉ trả về 1 sản phẩm.

#### 3.3 Add Product
**POST** `/v1/product`

Thêm sản phẩm mới.

**Request Body:**
```json
{
  "productCode": "PROD001",
  "productName": "Widget A",
  "productWeight": 2.5,
  "injectionTime": 30.0,
  "color": "Red",
  "unitPrice": 100.00
}
```

#### 3.4 Add Stock
**POST** `/v1/product/addstock`

Thêm tồn kho cho sản phẩm.

**Request Body:**
```json
{
  "productID": 1,
  "quantity": 100.0,
  "note": "Initial stock"
}
```

#### 3.5 Reduce Stock
**POST** `/v1/product/reducestock`

Giảm tồn kho sản phẩm.

**Request Body:**
```json
{
  "productID": 1,
  "quantity": 50.0,
  "note": "Sold items"
}
```

### 4. BOM (Bill of Materials) APIs

#### 4.1 Get BOM Entries
**GET** `/v1/bom`

Lấy danh sách BOM entries.

**Response:**
```json
{
  "code": 200,
  "data": [
    {
      "materialCode": "MAT001",
      "MaterialName": "Steel Rod",
      "materialQuantity": 2.0,
      "Supplier": "ABC Steel Co.",
      "UnitCost": 15.50,
      "Unit": "kg"
    }
  ],
  "message": "success"
}
```

#### 4.2 Add BOM Entries
**POST** `/v1/bom`

Thêm BOM entries mới.

**Request Body:**
```json
{
  "bomCode": "BOM001",
  "materialCode": "MAT001", 
  "materialQuantity": 2.0
}
```

#### 4.3 Get Product Cost
**GET** `/v1/bom/cost/{productCode}`

Tính toán chi phí sản phẩm dựa trên BOM.

**Parameters:**
- `productCode` (path): Mã sản phẩm

**Response:**
```json
{
  "code": 200,
  "data": {
    "productCode": "PROD001",
    "totalCost": 150.75,
    "materials": [
      {
        "materialCode": "MAT001",
        "quantity": 2.0,
        "unitCost": 15.50,
        "totalCost": 31.00
      }
    ]
  },
  "message": "success"
}
```

### 5. Order APIs

#### 5.1 Get All Orders
**GET** `/v1/order`

Lấy danh sách tất cả đơn hàng.

**Response:**
```json
{
  "code": 200,
  "data": [
    {
      "orderCode": "ORD001",
      "orderDate": "2024-01-01T00:00:00Z",
      "planDeliveryDate": "2024-01-15T00:00:00Z",
      "actualDeliveryDate": "2024-01-14T00:00:00Z",
      "orderStatus": "COMPLETED",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-14T00:00:00Z",
      "items": [
        {
          "productId": 1,
          "productQuantity": 100.0,
          "orderPartStatus": "SHIPPED",
          "shippedQuantity": 100,
          "createdAt": "2024-01-01T00:00:00Z"
        }
      ]
    }
  ],
  "message": "success"
}
```

#### 5.2 Get Order by Code
**GET** `/v1/order/{orderCode}`

Lấy thông tin đơn hàng theo mã đơn hàng.

**Parameters:**
- `orderCode` (path): Mã đơn hàng

**Response:** Tương tự Get All Orders nhưng chỉ trả về 1 đơn hàng.

#### 5.3 Add Order
**POST** `/v1/order`

Tạo đơn hàng mới.

**Request Body:**
```json
{
  "orderCode": "ORD001",
  "orderDate": "2024-01-01T00:00:00Z",
  "planDeliveryDate": "2024-01-15T00:00:00Z",
  "orderStatus": "PENDING",
  "items": [
    {
      "productId": 1,
      "productQuantity": 100.0,
      "orderPartStatus": "PENDING"
    }
  ]
}
```

#### 5.4 Update Order
**PUT** `/v1/order/{orderId}`

Cập nhật thông tin đơn hàng.

**Parameters:**
- `orderId` (path): ID của đơn hàng

**Request Body:** Tương tự Add Order

## Error Handling

### Error Response Format
```json
{
  "code": 400,
  "data": null,
  "message": "Error message description"
}
```

### Common Error Codes
- `400` - Bad Request: Dữ liệu đầu vào không hợp lệ
- `401` - Unauthorized: Chưa xác thực hoặc token không hợp lệ
- `404` - Not Found: Tài nguyên không tồn tại
- `500` - Internal Server Error: Lỗi server

## Data Models

### Material
```json
{
  "ID": "integer",
  "MaterialCode": "string",
  "MaterialName": "string",
  "Supplier": "string",
  "UnitCost": "float64",
  "Unit": "string",
  "CreatedAt": "datetime",
  "Deleted": "boolean"
}
```

### Product
```json
{
  "id": "integer",
  "productCode": "string",
  "productName": "string",
  "productWeight": "float64",
  "injectionTime": "float64",
  "color": "string",
  "unitPrice": "float64",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

### BOM Entry
```json
{
  "bomCode": "string",
  "materialCode": "string",
  "materialQuantity": "float64"
}
```

### Order
```json
{
  "orderCode": "string",
  "orderDate": "datetime",
  "planDeliveryDate": "datetime",
  "actualDeliveryDate": "datetime",
  "orderStatus": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "items": "array of OrderDetail"
}
```

### Order Detail
```json
{
  "productId": "integer",
  "productQuantity": "float64",
  "orderPartStatus": "string",
  "shippedQuantity": "integer",
  "createdAt": "datetime"
}
```

## Status Values

### Order Status
- `PENDING` - Đang chờ xử lý
- `PROCESSING` - Đang xử lý
- `SHIPPED` - Đã giao hàng
- `COMPLETED` - Hoàn thành
- `CANCELLED` - Đã hủy

### Order Part Status
- `PENDING` - Đang chờ
- `PROCESSING` - Đang xử lý
- `SHIPPED` - Đã giao
- `COMPLETED` - Hoàn thành

### Movement Type (Product Movement)
- `IN` - Nhập kho
- `OUT` - Xuất kho
- `ADJUST` - Điều chỉnh

## Configuration

### Database Configuration
API sử dụng file config tại `data/config.yml` để cấu hình database và server.

### Logging
- Application logs: `log/application.log`
- Database logs: `log/database.log`
- System logs: `log/system.log`

## Development Notes

### Technology Stack
- **Language:** Go
- **Framework:** Gin
- **ORM:** GORM
- **Database:** Configurable (MySQL/PostgreSQL/SQLite)

### Project Structure
```
├── cmd/                    # Application entry point
├── internal/
│   ├── api/
│   │   ├── controllers/    # HTTP handlers
│   │   ├── middlewares/    # HTTP middlewares
│   │   ├── routers/        # Route definitions
│   │   └── services/       # Business logic
│   └── pkg/
│       ├── config/         # Configuration management
│       ├── database/       # Database connection
│       └── models/         # Data models
├── data/                   # Configuration files
└── log/                    # Log files
```

### Key Features
- RESTful API design
- CORS support
- Request/Response logging
- Error handling middleware
- Database connection pooling
- Token-based authentication

### Running the Application
1. Ensure Go is installed (version 1.19+)
2. Configure database settings in `data/config.yml`
3. Run: `go run cmd/main.go`
4. API will be available at configured port

### Testing
Use tools like Postman, curl, or any HTTP client to test the endpoints.

Example curl command:
```bash
# Health check
curl -X GET http://localhost:8080/api/ping

# Login
curl -X POST http://localhost:8080/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{"UserID":"admin","Password":"password"}'

# Get materials
curl -X GET http://localhost:8080/api/v1/material \
  -H "Authorization: Bearer {token}"
```
