build:
	go build -o bin/web-api cmd/main.go

run:
	go run cmd/main.go

format:
	go fmt web-api/...

build-docker:
	docker build . -t web-api

run-docker:
	docker run -itd --name web-api --restart always -p 8081:8081 web-api

exec-docker:
	docker exec -it web-api sh

# Testing commands
test:
	go test ./tests/... -v

test-controllers:
	go test ./tests/controllers/... -v

test-integration:
	INTEGRATION_TESTS=true go test ./tests/integration/... -v

test-coverage:
	go test ./tests/... -v -coverprofile=coverage.out
	go tool cover -html=coverage.out -o coverage.html

test-race:
	go test ./tests/... -v -race

test-bench:
	go test ./tests/... -v -bench=.

test-clean:
	go clean -testcache
	rm -f coverage.out coverage.html test.db

# Run all tests
test-all: test-clean test test-integration test-coverage

# Quick test (unit tests only)
test-quick:
	go test ./tests/controllers/... -v -short

# Database operations
reset-db:
	@echo "🗑️  Resetting database and seeding fresh data..."
	go run cmd/reset_db/main.go

seed-db: reset-db

# Development helpers
dev:
	@echo "🔥 Starting development server..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "❌ Air not found. Falling back to normal run..."; \
		make run; \
	fi

help:
	@echo "🚀 ERP API Commands"
	@echo "=================="
	@echo "make run        - Run the API server"
	@echo "make build      - Build the application"
	@echo "make test       - Run all tests"
	@echo "make reset-db   - Reset database and seed with fresh data"
	@echo "make dev        - Run in development mode with hot reload"