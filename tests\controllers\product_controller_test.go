package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"web-api/internal/api/controllers"
	"web-api/internal/pkg/models/request"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type ProductControllerTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *ProductControllerTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
	
	// Setup routes
	v1 := suite.router.Group("/api/v1")
	productGroup := v1.Group("/product")
	{
		productGroup.GET("", controllers.ProductControllerInstance.GetProducts)
		productGroup.GET("/:productCode", controllers.ProductControllerInstance.GetProducts)
		productGroup.POST("", controllers.ProductControllerInstance.AddProduct)
		productGroup.POST("/addstock", controllers.ProductControllerInstance.AddStock)
		productGroup.POST("/reducestock", controllers.ProductControllerInstance.ReduceStock)
	}
}

func (suite *ProductControllerTestSuite) TestGetProducts_Success() {
	req, _ := http.NewRequest("GET", "/api/v1/product", nil)
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	
	assert.Contains(suite.T(), response, "code")
	assert.Contains(suite.T(), response, "data")
	assert.Contains(suite.T(), response, "message")
}

func (suite *ProductControllerTestSuite) TestGetProductByCode_Success() {
	req, _ := http.NewRequest("GET", "/api/v1/product/PROD001", nil)
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *ProductControllerTestSuite) TestAddProduct_Success() {
	product := request.AddProductRequest{
		ProductCode:   "PROD001",
		ProductName:   "Test Product",
		ProductWeight: 2.5,
		InjectionTime: 30.0,
		Color:         "Red",
		UnitPrice:     100.00,
	}

	jsonData, _ := json.Marshal(product)
	req, _ := http.NewRequest("POST", "/api/v1/product", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	
	assert.Equal(suite.T(), float64(200), response["code"])
}

func (suite *ProductControllerTestSuite) TestAddProduct_InvalidJSON() {
	invalidJSON := `{"ProductCode": "PROD001", "ProductName":}`

	req, _ := http.NewRequest("POST", "/api/v1/product", bytes.NewBufferString(invalidJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

func (suite *ProductControllerTestSuite) TestAddStock_Success() {
	stockRequest := request.StockRequest{
		ProductID: 1,
		Quantity:  100.0,
		Note:      "Initial stock",
	}

	jsonData, _ := json.Marshal(stockRequest)
	req, _ := http.NewRequest("POST", "/api/v1/product/addstock", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *ProductControllerTestSuite) TestReduceStock_Success() {
	stockRequest := request.StockRequest{
		ProductID: 1,
		Quantity:  50.0,
		Note:      "Sold items",
	}

	jsonData, _ := json.Marshal(stockRequest)
	req, _ := http.NewRequest("POST", "/api/v1/product/reducestock", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *ProductControllerTestSuite) TestAddStock_InvalidData() {
	stockRequest := request.StockRequest{
		ProductID: 0, // Invalid product ID
		Quantity:  -10.0, // Negative quantity
	}

	jsonData, _ := json.Marshal(stockRequest)
	req, _ := http.NewRequest("POST", "/api/v1/product/addstock", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	// Depending on validation logic, this should fail
	assert.NotEqual(suite.T(), http.StatusOK, w.Code)
}

func TestProductControllerTestSuite(t *testing.T) {
	suite.Run(t, new(ProductControllerTestSuite))
}

// Benchmark tests
func BenchmarkGetProducts(b *testing.B) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/products", controllers.ProductControllerInstance.GetProducts)

	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("GET", "/products", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}

func BenchmarkAddProduct(b *testing.B) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/products", controllers.ProductControllerInstance.AddProduct)

	product := request.AddProductRequest{
		ProductCode:   "BENCH001",
		ProductName:   "Benchmark Product",
		ProductWeight: 1.0,
		InjectionTime: 10.0,
		Color:         "Blue",
		UnitPrice:     50.00,
	}
	jsonData, _ := json.Marshal(product)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("POST", "/products", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
	}
}
