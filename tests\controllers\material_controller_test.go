package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"web-api/internal/api/controllers"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type MaterialControllerTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (suite *MaterialControllerTestSuite) SetupSuite() {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)
	
	// Create router
	suite.router = gin.New()
	
	// Setup routes
	v1 := suite.router.Group("/api/v1")
	materialGroup := v1.Group("/material")
	{
		materialGroup.GET("", controllers.Material.GetMaterials)
		materialGroup.POST("", controllers.Material.AddMaterial)
	}
}

func (suite *MaterialControllerTestSuite) TestGetMaterials_Success() {
	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/material", nil)
	w := httptest.NewRecorder()

	// Perform request
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	
	// Check response structure
	assert.Contains(suite.T(), response, "code")
	assert.Contains(suite.T(), response, "data")
	assert.Contains(suite.T(), response, "message")
}

func (suite *MaterialControllerTestSuite) TestAddMaterial_Success() {
	// Create test material
	material := types.Material{
		MaterialCode: "TEST001",
		MaterialName: "Test Material",
		Supplier:     "Test Supplier",
		UnitCost:     10.50,
		Unit:         "kg",
	}

	// Convert to JSON
	jsonData, _ := json.Marshal(material)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/material", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Perform request
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusOK, w.Code)
	
	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	
	// Check response
	assert.Equal(suite.T(), float64(200), response["code"])
	assert.Contains(suite.T(), response["message"], "successfully")
}

func (suite *MaterialControllerTestSuite) TestAddMaterial_InvalidJSON() {
	// Create invalid JSON
	invalidJSON := `{"MaterialCode": "TEST001", "MaterialName":}`

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/material", bytes.NewBufferString(invalidJSON))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Perform request
	suite.router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
	
	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	
	// Check error response
	assert.Equal(suite.T(), float64(400), response["code"])
	assert.Contains(suite.T(), response["message"], "Invalid request body")
}

func (suite *MaterialControllerTestSuite) TestAddMaterial_MissingFields() {
	// Create material with missing required fields
	material := types.Material{
		MaterialCode: "", // Missing required field
		MaterialName: "Test Material",
	}

	// Convert to JSON
	jsonData, _ := json.Marshal(material)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/material", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Perform request
	suite.router.ServeHTTP(w, req)

	// The actual behavior depends on your validation logic
	// This test assumes validation is implemented
	assert.NotEqual(suite.T(), http.StatusOK, w.Code)
}

// Run the test suite
func TestMaterialControllerTestSuite(t *testing.T) {
	suite.Run(t, new(MaterialControllerTestSuite))
}

// Individual test functions (can be run separately)
func TestMaterialController_GetMaterials(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/materials", controllers.Material.GetMaterials)

	req, _ := http.NewRequest("GET", "/materials", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestMaterialController_AddMaterial_ValidData(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/materials", controllers.Material.AddMaterial)

	material := types.Material{
		MaterialCode: "MAT001",
		MaterialName: "Steel Rod",
		Supplier:     "ABC Steel Co.",
		UnitCost:     15.50,
		Unit:         "kg",
	}

	jsonData, _ := json.Marshal(material)
	req, _ := http.NewRequest("POST", "/materials", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}
