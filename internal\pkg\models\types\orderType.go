package types

import "time"

// Order 對應到 orders 資料表
type Order struct {
	ID                 int64         `json:"-" gorm:"primaryKey;column:id"` // 不輸出 ID
	OrderCode          string        `json:"orderCode" gorm:"column:order_code;unique;not null"`
	OrderDate          time.Time     `json:"orderDate" gorm:"column:order_date"`
	PlanDeliveryDate   time.Time     `json:"planDeliveryDate" gorm:"column:plan_delivery_date"`
	ActualDeliveryDate time.Time     `json:"actualDeliveryDate" gorm:"column:actual_delivery_date"`
	OrderStatus        string        `json:"orderStatus" gorm:"column:order_status"`
	CreatedAt          time.Time     `json:"createdAt" gorm:"column:created_at;autoCreateTime"`
	UpdatedAt          time.Time     `json:"updatedAt" gorm:"column:updated_at;autoUpdateTime"`
	Items              []OrderDetail `json:"items" gorm:"foreignKey:OrderID"`
}

// OrderDetail 對應到 order_details 資料表
type OrderDetail struct {
	ID              int64     `json:"orderDetailId" gorm:"primaryKey;column:id"` // Include ID for updates
	OrderID         int64     `json:"-" gorm:"column:order_id;not null"`         // 不輸出 OrderID
	ProductID       int64     `json:"productId" gorm:"column:product_id;not null"`
	ProductQuantity float64   `json:"productQuantity" gorm:"column:product_quantity"`
	OrderPartStatus string    `json:"orderPartStatus" gorm:"column:order_part_status"`
	ShippedQuantity int64     `json:"shippedQuantity" gorm:"column:shipped_quantity"`
	CreatedAt       time.Time `json:"createdAt" gorm:"column:created_at;autoCreateTime"`
}
