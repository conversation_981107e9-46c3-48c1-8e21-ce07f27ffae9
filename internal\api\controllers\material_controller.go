package controllers

import (
	"errors"
	"log"
	"net/http"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/errs"
	"web-api/internal/pkg/models/response"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
)

type MaterialController struct {
	*BaseController
}

var Material = &MaterialController{}

// 取得所有材料
func (m *MaterialController) GetMaterials(ctx *gin.Context) {
	var payload types.Material // 若有查詢條件，可擴充

	result, err := services.Material.GetAllMaterials(&payload)
	if err != nil {
		log.Printf("Failed to get materials: %v", err)
		response.FailWithMessage(ctx, "Failed to get materials")
		return
	}
	response.OkWithData(ctx, result)
}

// 新增材料
func (m *MaterialController) AddMaterial(ctx *gin.Context) {
	var payload types.Material
	if err := ctx.ShouldBindJSON(&payload); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body")
		return
	}

	err := services.Material.AddMaterial(&payload)
	if err != nil {
		if errors.Is(err, errs.ErrMaterialExists) {
			response.OkWithMessage(ctx, "Material already exists, skipping insert.")
			return
		}
		response.FailWithMessage(ctx, "Failed to add material")
		return
	}

	response.OkWithMessage(ctx, "Material added successfully")
}
