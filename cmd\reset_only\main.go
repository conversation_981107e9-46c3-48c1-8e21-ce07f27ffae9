package main

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🗑️  ERP Database Reset Tool (No Seeding)")
	fmt.Println("=========================================")

	// Connect to database directly
	fmt.Println("📡 Connecting to database...")
	dsn := "host=localhost user=postgres password=123456 dbname=erp_db port=5432 sslmode=disable TimeZone=Asia/Ho_Chi_Minh"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	fmt.Println("✅ Database connected successfully")

	// Clean existing data
	fmt.Println("\n🗑️  Cleaning existing data...")
	if err := cleanExistingData(db); err != nil {
		log.Fatal("Failed to clean existing data:", err)
	}

	fmt.Println("\n✅ Database reset completed successfully!")
	fmt.Println("📊 All tables are now empty and ready for fresh data")
}

func cleanExistingData(db *gorm.DB) error {
	// Delete data in correct order (respecting foreign key constraints)
	tables := []string{
		"order_details",
		"orders",
		"product_movements",
		"product_inventories",
		"product_bom",
		"products",
		"materials",
	}

	for _, table := range tables {
		result := db.Exec(fmt.Sprintf("DELETE FROM %s", table))
		if result.Error != nil {
			fmt.Printf("⚠️  Warning: Could not clean table %s: %v\n", table, result.Error)
		} else {
			fmt.Printf("✅ Cleaned table: %s (%d rows deleted)\n", table, result.RowsAffected)
		}
	}

	// Reset sequences for PostgreSQL
	sequences := []string{
		"materials_id_seq",
		"products_id_seq",
		"product_inventories_id_seq",
		"product_movements_id_seq",
		"orders_id_seq",
		"order_details_id_seq",
		"product_bom_id_seq",
	}

	for _, seq := range sequences {
		result := db.Exec(fmt.Sprintf("ALTER SEQUENCE IF EXISTS %s RESTART WITH 1", seq))
		if result.Error != nil {
			fmt.Printf("⚠️  Warning: Could not reset sequence %s: %v\n", seq, result.Error)
		} else {
			fmt.Printf("✅ Reset sequence: %s\n", seq)
		}
	}

	return nil
}
