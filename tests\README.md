# ERP API Testing Guide

## Tổng quan
Thư mục này chứa tất cả các test cases cho ERP API, bao gồm unit tests, integration tests và test utilities.

## Cấu trúc thư mục
```
tests/
├── controllers/          # Unit tests cho controllers
│   ├── material_controller_test.go
│   ├── product_controller_test.go
│   ├── bom_controller_test.go
│   ├── order_controller_test.go
│   └── common_controller_test.go
├── integration/          # Integration tests
│   └── api_integration_test.go
├── helpers/              # Test utilities và helpers
│   └── test_helpers.go
├── test_config.go        # Test configuration
└── README.md            # Tài liệu này
```

## Yêu cầu
- Go 1.19+
- Testing dependencies (đã được cài đặt):
  - github.com/stretchr/testify
  - github.com/gin-gonic/gin

## Chạy Tests

### 1. Chạy tất cả tests
```bash
make test
```

### 2. Chạy unit tests cho controllers
```bash
make test-controllers
```

### 3. Chạy integration tests
```bash
make test-integration
```

### 4. Chạy tests với coverage report
```bash
make test-coverage
```

### 5. Chạy tests với race detection
```bash
make test-race
```

### 6. Chạy benchmark tests
```bash
make test-bench
```

### 7. Chạy quick tests (unit tests only)
```bash
make test-quick
```

### 8. Clean test cache và files
```bash
make test-clean
```

## Các loại Tests

### Unit Tests
- **Material Controller Tests**: Test các API endpoints cho quản lý vật liệu
- **Product Controller Tests**: Test các API endpoints cho quản lý sản phẩm
- **BOM Controller Tests**: Test các API endpoints cho quản lý BOM
- **Order Controller Tests**: Test các API endpoints cho quản lý đơn hàng
- **Common Controller Tests**: Test các API endpoints chung (ping, login)

### Integration Tests
- **API Integration Tests**: Test toàn bộ workflow từ request đến response
- **Database Integration**: Test tương tác với database
- **Authentication Flow**: Test luồng xác thực

## Test Patterns

### 1. Test Suites
Sử dụng testify/suite để tổ chức tests:
```go
type MaterialControllerTestSuite struct {
    suite.Suite
    router *gin.Engine
}

func (suite *MaterialControllerTestSuite) SetupSuite() {
    // Setup code
}

func (suite *MaterialControllerTestSuite) TestGetMaterials_Success() {
    // Test implementation
}
```

### 2. Table-driven Tests
Sử dụng cho testing nhiều scenarios:
```go
tests := []struct {
    name           string
    input          interface{}
    expectedStatus int
}{
    {"Valid Input", validInput, http.StatusOK},
    {"Invalid Input", invalidInput, http.StatusBadRequest},
}

for _, tt := range tests {
    t.Run(tt.name, func(t *testing.T) {
        // Test implementation
    })
}
```

### 3. Test Helpers
Sử dụng helpers để giảm code duplication:
```go
helper := helpers.NewTestHelper(router, t)
response := helper.MakeRequest("GET", "/api/v1/material", nil, nil)
helper.AssertSuccessResponse(response)
```

## Test Data Management

### Test Data Creation
Sử dụng helper functions để tạo test data:
```go
material := helpers.CreateTestMaterial("TEST001")
product := helpers.CreateTestProduct("PROD001")
order := helpers.CreateTestOrder("ORD001")
```

### Test Data Cleanup
Sử dụng cleanup utilities:
```go
cleanup := helpers.NewTestDataCleanup()
cleanup.AddMaterial("TEST001")
defer cleanup.Cleanup()
```

## Environment Variables

### Test Configuration
- `INTEGRATION_TESTS=true`: Chạy integration tests
- `TEST_PORT=8081`: Port cho test server
- `TEST_DATABASE_URL`: Database URL cho tests
- `GIN_MODE=test`: Set Gin mode to test

### Example
```bash
INTEGRATION_TESTS=true TEST_PORT=8082 go test ./tests/integration/... -v
```

## Mocking và Stubbing

### Database Mocking
Cho unit tests, có thể mock database calls:
```go
// Mock database service
mockService := &MockMaterialService{}
mockService.On("GetAllMaterials", mock.Anything).Return(materials, nil)
```

### HTTP Mocking
Sử dụng httptest để mock HTTP requests:
```go
req := httptest.NewRequest("GET", "/api/v1/material", nil)
w := httptest.NewRecorder()
router.ServeHTTP(w, req)
```

## Best Practices

### 1. Test Naming
- Sử dụng pattern: `TestFunction_Scenario_ExpectedResult`
- Ví dụ: `TestGetMaterials_ValidRequest_ReturnsSuccess`

### 2. Test Organization
- Mỗi controller có file test riêng
- Group related tests trong test suites
- Sử dụng subtests cho variations

### 3. Assertions
- Sử dụng testify/assert cho assertions rõ ràng
- Check cả status code và response structure
- Validate error messages

### 4. Test Data
- Sử dụng unique identifiers cho test data
- Clean up test data sau mỗi test
- Không depend vào external data

### 5. Performance
- Sử dụng benchmark tests cho performance-critical code
- Monitor test execution time
- Optimize slow tests

## Debugging Tests

### 1. Verbose Output
```bash
go test ./tests/... -v
```

### 2. Run Specific Test
```bash
go test ./tests/controllers/ -run TestMaterialController_GetMaterials
```

### 3. Debug với Delve
```bash
dlv test ./tests/controllers/ -- -test.run TestMaterialController_GetMaterials
```

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run Tests
  run: |
    make test-all
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage.out
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check test database configuration
   - Ensure test database is accessible
   - Use in-memory database for unit tests

2. **Port Conflicts**
   - Use different ports for tests
   - Set TEST_PORT environment variable

3. **Race Conditions**
   - Run tests with `-race` flag
   - Use proper synchronization in tests

4. **Flaky Tests**
   - Add proper setup/teardown
   - Use deterministic test data
   - Add timeouts for async operations

### Getting Help
- Check test logs for detailed error messages
- Use verbose mode for debugging
- Review test configuration files
- Ensure all dependencies are installed

## Contributing

### Adding New Tests
1. Follow existing test patterns
2. Add appropriate documentation
3. Ensure tests are deterministic
4. Include both positive and negative test cases
5. Update this README if needed

### Test Coverage Goals
- Aim for >80% code coverage
- Focus on critical business logic
- Test error handling paths
- Include edge cases
