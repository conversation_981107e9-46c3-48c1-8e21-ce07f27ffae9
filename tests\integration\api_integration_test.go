package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"web-api/internal/api/routers"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type APIIntegrationTestSuite struct {
	suite.Suite
	router *gin.Engine
	token  string
}

func (suite *APIIntegrationTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
	suite.router = router.Setup()
	
	// Login to get token for authenticated requests
	suite.login()
}

func (suite *APIIntegrationTestSuite) login() {
	user := types.User{
		UserID:   "testuser",
		Password: "testpass",
		RoleID:   "admin",
	}

	jsonData, _ := json.Marshal(user)
	req, _ := http.NewRequest("POST", "/api/v1/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if token, ok := data["Token"].(string); ok {
				suite.token = token
			}
		}
	}
}

func (suite *APIIntegrationTestSuite) TestPingEndpoint() {
	req, _ := http.NewRequest("GET", "/api/ping", nil)
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)

	assert.True(suite.T(), w.Code == http.StatusOK || w.Code == http.StatusInternalServerError)
}

func (suite *APIIntegrationTestSuite) TestMaterialWorkflow() {
	// Test adding a material
	material := types.Material{
		MaterialCode: "INT_MAT001",
		MaterialName: "Integration Test Material",
		Supplier:     "Test Supplier",
		UnitCost:     25.50,
		Unit:         "kg",
	}

	// Add material
	jsonData, _ := json.Marshal(material)
	req, _ := http.NewRequest("POST", "/api/v1/material", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Get materials to verify addition
	req, _ = http.NewRequest("GET", "/api/v1/material", nil)
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "data")
}

func (suite *APIIntegrationTestSuite) TestProductWorkflow() {
	// Test adding a product
	product := request.AddProductRequest{
		ProductCode:   "INT_PROD001",
		ProductName:   "Integration Test Product",
		ProductWeight: 1.5,
		InjectionTime: 25.0,
		Color:         "Blue",
		UnitPrice:     75.00,
	}

	// Add product
	jsonData, _ := json.Marshal(product)
	req, _ := http.NewRequest("POST", "/api/v1/product", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Get products to verify addition
	req, _ = http.NewRequest("GET", "/api/v1/product", nil)
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Test getting specific product
	req, _ = http.NewRequest("GET", "/api/v1/product/INT_PROD001", nil)
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *APIIntegrationTestSuite) TestBOMWorkflow() {
	// Test adding BOM entry
	bomEntry := types.BOMEntry{
		BOMCode:          "INT_BOM001",
		MaterialCode:     "INT_MAT001",
		MaterialQuantity: 3.0,
	}

	// Add BOM entry
	jsonData, _ := json.Marshal(bomEntry)
	req, _ := http.NewRequest("POST", "/api/v1/bom", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Get BOM entries
	req, _ = http.NewRequest("GET", "/api/v1/bom", nil)
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Test product cost calculation
	req, _ = http.NewRequest("GET", "/api/v1/bom/cost/INT_PROD001", nil)
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *APIIntegrationTestSuite) TestOrderWorkflow() {
	// Test adding an order
	order := types.Order{
		OrderCode:        "INT_ORD001",
		OrderDate:        time.Now(),
		PlanDeliveryDate: time.Now().AddDate(0, 0, 14),
		OrderStatus:      "PENDING",
		Items: []types.OrderDetail{
			{
				ProductID:       1,
				ProductQuantity: 50.0,
				OrderPartStatus: "PENDING",
			},
		},
	}

	// Add order
	jsonData, _ := json.Marshal(order)
	req, _ := http.NewRequest("POST", "/api/v1/order", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Get orders
	req, _ = http.NewRequest("GET", "/api/v1/order", nil)
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Get specific order
	req, _ = http.NewRequest("GET", "/api/v1/order/INT_ORD001", nil)
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func (suite *APIIntegrationTestSuite) TestStockManagement() {
	// Test adding stock
	stockRequest := request.StockRequest{
		ProductID: 1,
		Quantity:  200.0,
		Note:      "Integration test stock addition",
	}

	jsonData, _ := json.Marshal(stockRequest)
	req, _ := http.NewRequest("POST", "/api/v1/product/addstock", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w := httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Test reducing stock
	reduceRequest := request.StockRequest{
		ProductID: 1,
		Quantity:  50.0,
		Note:      "Integration test stock reduction",
	}

	jsonData, _ = json.Marshal(reduceRequest)
	req, _ = http.NewRequest("POST", "/api/v1/product/reducestock", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	if suite.token != "" {
		req.Header.Set("Authorization", "Bearer "+suite.token)
	}
	w = httptest.NewRecorder()

	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func TestAPIIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(APIIntegrationTestSuite))
}
