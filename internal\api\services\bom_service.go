package services

import (
	"errors"
	"fmt"
	"web-api/internal/pkg/database"
	"web-api/internal/pkg/models/types"

	"gorm.io/gorm"
)

type ProductBOMService struct{}

var BOMService = &ProductBOMService{}

func (s *ProductBOMService) GetProductBOMEntries(productCode string) ([]types.BOMEntryDetail, error) {
	db, err := database.DatabaseConnection()
	if err != nil {
		return nil, err
	}
	sqlDB, _ := db.DB()
	defer sqlDB.Close()

	var results []types.BOMEntryDetail

	query := `
		SELECT 
			p.product_code AS product_code,
			p.product_name AS product_name,
			m.material_code AS material_code,
			m.material_name AS material_name,
			m.supplier AS supplier,
			m.unit_cost AS unit_cost,
			m.unit AS unit,
			pb.material_quantity AS material_quantity
		FROM product_bom pb
		JOIN products p ON pb.product_id = p.id
		JOIN materials m ON pb.material_id = m.id
		WHERE p.product_code = $1
	`

	err = db.Raw(query, productCode).Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (s *ProductBOMService) AddBOMEntries(productCode string, entries []types.BOMEntry) error {
	db, err := database.DatabaseConnection()
	if err != nil {
		fmt.Println("Database connection error:", err)
		return err
	}
	defer func() {
		if sqlDB, _ := db.DB(); sqlDB != nil {
			sqlDB.Close()
		}
	}()

	// 查產品 ID
	var productID int
	err = db.Table("products").Select("id").Where("product_code = ?", productCode).Scan(&productID).Error
	if err != nil || productID == 0 {
		return fmt.Errorf("product %s not found", productCode)
	}

	for _, entry := range entries {
		// 查材料 ID
		var materialID int
		err = db.Table("materials").Select("id").Where("material_code = ?", entry.MaterialCode).Scan(&materialID).Error
		if err != nil || materialID == 0 {
			return fmt.Errorf("material %s not found", entry.MaterialCode)
		}

		// 檢查是否已存在相同 BOM 記錄
		var existing int64
		err = db.Table("product_bom").Where("product_id = ? AND material_id = ?", productID, materialID).Count(&existing).Error
		if err != nil {
			fmt.Println("Query BOM existence error:", err)
			return err
		}
		if existing > 0 {
			return fmt.Errorf("BOM entry for product %s and material %s already exists", productCode, entry.MaterialCode)
		}

		// 取得最後一筆 bom_code
		var lastCode string
		err = db.Raw(`SELECT bom_code FROM product_bom ORDER BY bom_code DESC LIMIT 1`).Scan(&lastCode).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			fmt.Println("Failed to get last bom_code:", err)
			return err
		}
		newBOMCode := generateNextCode("B", lastCode)

		// 寫入 BOM 表
		err = db.Exec(`
            INSERT INTO product_bom (product_id, material_id, material_quantity, bom_code)
            VALUES (?, ?, ?, ?)
        `, productID, materialID, entry.MaterialQuantity, newBOMCode).Error
		if err != nil {
			fmt.Println("Insert BOM error:", err)
			return err
		}

		fmt.Printf("Inserted BOM: product=%s, material=%s, bom_code=%s\n", productCode, entry.MaterialCode, newBOMCode)
	}

	return nil
}

func (s *ProductBOMService) CalculateProductCost(productCode string) (float64, error) {

	db, err := database.DatabaseConnection()
	if err != nil {
		return 0, err
	}
	defer func() {
		if sqlDB, _ := db.DB(); sqlDB != nil {
			sqlDB.Close()
		}
	}()

	var totalCost float64

	query := `
		SELECT SUM(m.unit_cost * pb.material_quantity) AS total_cost
		FROM product_bom pb
		JOIN products p ON pb.product_id = p.id
		JOIN materials m ON pb.material_id = m.id
		WHERE p.product_code = $1
	`

	err = db.Raw(query, productCode).Scan(&totalCost).Error
	if err != nil {
		return 0, err
	}

	return totalCost, nil
}
