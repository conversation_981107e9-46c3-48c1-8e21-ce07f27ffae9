package request

type AddProductRequest struct {
	ProductCode   string  `json:"productCode" gorm:"column:product_code"`
	ProductName   string  `json:"productName" gorm:"column:product_name"`
	ProductWeight float64 `json:"productWeight" gorm:"column:product_weight"`
	InjectionTime float64 `json:"injectionTime" gorm:"column:injection_time"`
	Color         string  `json:"color" gorm:"column:color"`
	UnitPrice     float64 `json:"unitPrice" gorm:"column:unit_price"`
}
type StockRequest struct {
	ProductID int64   `json:"productID"` // 產品ID，通常為整數
	Quantity  float64 `json:"quantity"`  // 數量，通常為浮點數或整數
	Note      string  `json:"note"`
}
