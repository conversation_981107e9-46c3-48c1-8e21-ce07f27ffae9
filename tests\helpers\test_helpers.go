package helpers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/types"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestHelper provides common testing utilities
type TestHelper struct {
	Router *gin.Engine
	T      *testing.T
}

// NewTestHelper creates a new test helper instance
func NewTestHelper(router *gin.Engine, t *testing.T) *TestHelper {
	return &TestHelper{
		Router: router,
		T:      t,
	}
}

// MakeRequest performs an HTTP request and returns the response recorder
func (h *TestHelper) MakeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody *bytes.Buffer
	
	if body != nil {
		jsonData, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonData)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req, _ := http.NewRequest(method, url, reqBody)
	
	// Set default content type for POST/PUT requests
	if method == "POST" || method == "PUT" {
		req.Header.Set("Content-Type", "application/json")
	}
	
	// Set additional headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	h.Router.ServeHTTP(w, req)
	
	return w
}

// AssertJSONResponse checks if response is valid JSON and contains expected fields
func (h *TestHelper) AssertJSONResponse(w *httptest.ResponseRecorder, expectedCode int) map[string]interface{} {
	assert.Equal(h.T, expectedCode, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(h.T, err)
	
	assert.Contains(h.T, response, "code")
	assert.Contains(h.T, response, "message")
	
	return response
}

// AssertSuccessResponse checks for successful API response
func (h *TestHelper) AssertSuccessResponse(w *httptest.ResponseRecorder) map[string]interface{} {
	response := h.AssertJSONResponse(w, http.StatusOK)
	assert.Equal(h.T, float64(200), response["code"])
	return response
}

// AssertErrorResponse checks for error API response
func (h *TestHelper) AssertErrorResponse(w *httptest.ResponseRecorder, expectedCode int) map[string]interface{} {
	response := h.AssertJSONResponse(w, expectedCode)
	assert.NotEqual(h.T, float64(200), response["code"])
	return response
}

// CreateTestMaterial creates a test material object
func CreateTestMaterial(code string) types.Material {
	return types.Material{
		MaterialCode: code,
		MaterialName: "Test Material " + code,
		Supplier:     "Test Supplier",
		UnitCost:     10.50,
		Unit:         "kg",
	}
}

// CreateTestProduct creates a test product object
func CreateTestProduct(code string) request.AddProductRequest {
	return request.AddProductRequest{
		ProductCode:   code,
		ProductName:   "Test Product " + code,
		ProductWeight: 2.5,
		InjectionTime: 30.0,
		Color:         "Red",
		UnitPrice:     100.00,
	}
}

// CreateTestBOMEntry creates a test BOM entry object
func CreateTestBOMEntry(bomCode, materialCode string) types.BOMEntry {
	return types.BOMEntry{
		BOMCode:          bomCode,
		MaterialCode:     materialCode,
		MaterialQuantity: 2.0,
	}
}

// CreateTestOrder creates a test order object
func CreateTestOrder(orderCode string) types.Order {
	return types.Order{
		OrderCode:        orderCode,
		OrderDate:        time.Now(),
		PlanDeliveryDate: time.Now().AddDate(0, 0, 7),
		OrderStatus:      "PENDING",
		Items: []types.OrderDetail{
			{
				ProductID:       1,
				ProductQuantity: 100.0,
				OrderPartStatus: "PENDING",
			},
		},
	}
}

// CreateTestStockRequest creates a test stock request object
func CreateTestStockRequest(productID int64, quantity float64) request.StockRequest {
	return request.StockRequest{
		ProductID: productID,
		Quantity:  quantity,
		Note:      "Test stock operation",
	}
}

// CreateTestUser creates a test user object
func CreateTestUser(userID string) types.User {
	return types.User{
		UserID:   userID,
		UserName: "Test User " + userID,
		Password: "testpassword",
		RoleID:   "admin",
	}
}

// LoginAndGetToken performs login and returns the token
func (h *TestHelper) LoginAndGetToken(user types.User) string {
	w := h.MakeRequest("POST", "/api/v1/login", user, nil)
	
	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		if data, ok := response["data"].(map[string]interface{}); ok {
			if token, ok := data["Token"].(string); ok {
				return token
			}
		}
	}
	return ""
}

// MakeAuthenticatedRequest makes a request with authentication token
func (h *TestHelper) MakeAuthenticatedRequest(method, url string, body interface{}, token string) *httptest.ResponseRecorder {
	headers := map[string]string{}
	if token != "" {
		headers["Authorization"] = "Bearer " + token
	}
	return h.MakeRequest(method, url, body, headers)
}

// TestDataCleanup provides cleanup functionality for test data
type TestDataCleanup struct {
	MaterialCodes []string
	ProductCodes  []string
	OrderCodes    []string
	BOMCodes      []string
}

// NewTestDataCleanup creates a new cleanup instance
func NewTestDataCleanup() *TestDataCleanup {
	return &TestDataCleanup{
		MaterialCodes: make([]string, 0),
		ProductCodes:  make([]string, 0),
		OrderCodes:    make([]string, 0),
		BOMCodes:      make([]string, 0),
	}
}

// AddMaterial adds a material code to cleanup list
func (c *TestDataCleanup) AddMaterial(code string) {
	c.MaterialCodes = append(c.MaterialCodes, code)
}

// AddProduct adds a product code to cleanup list
func (c *TestDataCleanup) AddProduct(code string) {
	c.ProductCodes = append(c.ProductCodes, code)
}

// AddOrder adds an order code to cleanup list
func (c *TestDataCleanup) AddOrder(code string) {
	c.OrderCodes = append(c.OrderCodes, code)
}

// AddBOM adds a BOM code to cleanup list
func (c *TestDataCleanup) AddBOM(code string) {
	c.BOMCodes = append(c.BOMCodes, code)
}

// Cleanup performs cleanup operations (implement based on your database setup)
func (c *TestDataCleanup) Cleanup() {
	// This would typically involve database cleanup operations
	// Implementation depends on your database setup and ORM
	// Example:
	// for _, code := range c.MaterialCodes {
	//     database.DeleteMaterial(code)
	// }
	// for _, code := range c.ProductCodes {
	//     database.DeleteProduct(code)
	// }
	// etc.
}

// ValidateResponseStructure validates common response structure
func ValidateResponseStructure(t *testing.T, response map[string]interface{}) {
	assert.Contains(t, response, "code")
	assert.Contains(t, response, "data")
	assert.Contains(t, response, "message")
	
	// Validate types
	assert.IsType(t, float64(0), response["code"])
	assert.IsType(t, "", response["message"])
}

// GenerateUniqueCode generates a unique code for testing
func GenerateUniqueCode(prefix string) string {
	return prefix + "_" + time.Now().Format("20060102150405")
}

// CompareJSONObjects compares two JSON objects for equality
func CompareJSONObjects(t *testing.T, expected, actual interface{}) {
	expectedJSON, _ := json.Marshal(expected)
	actualJSON, _ := json.Marshal(actual)
	
	var expectedMap, actualMap map[string]interface{}
	json.Unmarshal(expectedJSON, &expectedMap)
	json.Unmarshal(actualJSON, &actualMap)
	
	assert.Equal(t, expectedMap, actualMap)
}
