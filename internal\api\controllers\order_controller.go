package controllers

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"web-api/internal/api/services"
	"web-api/internal/pkg/models/errs"
	"web-api/internal/pkg/models/request"
	"web-api/internal/pkg/models/response"

	"github.com/gin-gonic/gin"
)

type OrderController struct {
	*BaseController
}

var OrderControllerInstance = &OrderController{}

// 🚀 OPTIMIZED: Get orders with pagination support
func (m *OrderController) GetOrders(ctx *gin.Context) {

	orderCode := getOrderCode(ctx)

	var (
		result interface{}
		err    error
	)

	if orderCode == "" {
		// Get pagination parameters
		limitStr := ctx.DefaultQuery("limit", "50")  // Default 50 records
		offsetStr := ctx.DefaultQuery("offset", "0") // Default offset 0

		limit, _ := strconv.Atoi(limitStr)
		offset, _ := strconv.Atoi(offsetStr)

		// Validate limits for performance
		if limit > 100 {
			limit = 100 // Max 100 records per request
		}
		if limit <= 0 {
			limit = 50 // Default to 50
		}

		result, err = services.OrderServiceInstance.GetAllOrdersPaginated(limit, offset)
	} else {
		result, err = services.OrderServiceInstance.GetOrder(orderCode)
	}

	if err != nil {
		log.Printf("Failed to get order(s): %v", err)
		response.FailWithMessage(ctx, "Failed to get order(s)")
		return
	}

	response.OkWithData(ctx, result)
}

// 新增材料
func (m *OrderController) AddOrder(ctx *gin.Context) {

	var payload request.AddOrderRequest

	if err := ctx.ShouldBindJSON(&payload); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body: "+err.Error())
		return
	}

	err := services.OrderServiceInstance.AddOrder(&payload)
	if err != nil {
		var Err *errs.ErrProductNotExists
		if errors.As(err, &Err) {
			response.FailWithMessage(ctx, fmt.Sprintf("product with ID %d does not exist", Err.ProductID))
			return
		}

		response.FailWithMessage(ctx, "Failed to update order")
		return
	}

	response.OkWithMessage(ctx, "Product added successfully")
}

func (m *OrderController) UpdateOrder(ctx *gin.Context) {
	// Get orderCode from URL parameter
	orderCode := ctx.Param("orderCode")
	if orderCode == "" {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "orderCode is required")
		return
	}

	var payload request.UpdateOrderRequest

	if err := ctx.ShouldBindJSON(&payload); err != nil {
		response.FailWithDetailed(ctx, http.StatusBadRequest, nil, "Invalid request body: "+err.Error())
		return
	}

	// Set orderCode in payload
	payload.OrderCode = orderCode

	err := services.OrderServiceInstance.UpdateOrder(&payload)
	if err != nil {
		log.Printf("Failed to update order: %v", err)
		var OrderNotExistsError *errs.OrderNotExistsError
		if errors.As(err, &OrderNotExistsError) {
			response.FailWithMessage(ctx, fmt.Sprintf("order with ID %d does not exist", OrderNotExistsError.OrderID))
			return
		}
		var ErrProductExists *errs.ErrProductExists
		if errors.As(err, &ErrProductExists) {
			response.OkWithMessage(ctx, fmt.Sprintf("order with ID %d already exists exist, skipping insert.", ErrProductExists.ProductID))
			return
		}

		response.FailWithMessage(ctx, "Failed to update order")
		return
	}

	response.OkWithMessage(ctx, "update order successfully")
}
