package request

import (
	"time"
)

// 代表前端要傳的「建立訂單連同訂單明細」Payload
type AddOrderRequest struct {
	OrderDate        time.Time          `json:"orderDate" binding:"required"` // 訂單日期
	PlanDeliveryDate time.Time          `json:"planDeliveryDate"`
	OrderStatus      string             `json:"orderStatus" binding:"required"`
	Items            []OrderDetailInput `json:"items" binding:"required,dive,required"`
	PlanShipDate     time.Time          `json:"planShipDate,omitempty"` // 可選，計劃出貨日期
}
type UpdateOrderRequest struct {
	OrderID          int64             `json:"orderId"`
	OrderDate        time.Time         `json:"orderDate"`
	PlanDeliveryDate time.Time         `json:"planDeliveryDate"`
	OrderStatus      string            `json:"orderStatus"`
	NewItems         []AddOrderItem    `json:"newItems"`
	UpdatedItems     []UpdateOrderItem `json:"updatedItems"`
	DeletedItems     []DeletedItem     `json:"deletedItemIds"`
}

// 代表前端要傳的「訂單明細」格式（不含自動產生的 ID、CreatedAt 等）
type OrderDetailInput struct {
	ProductID       int64   `json:"productId" binding:"required"`
	ProductQuantity float64 `json:"productQuantity" binding:"required"`
	OrderPartStatus string  `json:"orderPartStatus" binding:"required"` // PENDING/CANCEL/DONE
}

type AddOrderItem struct {
	ProductID       int64   `json:"productId"`
	ProductQuantity float64 `json:"productQuantity"`
	OrderPartStatus string  `json:"orderPartStatus"`
}

type UpdateOrderItem struct {
	OrderDetailID   int64   `json:"orderDetailId"`
	ProductQuantity float64 `json:"productQuantity"`
	OrderPartStatus string  `json:"orderPartStatus"`
}
type DeletedItem struct {
	ProductID int64 `json:"productId"`
}
